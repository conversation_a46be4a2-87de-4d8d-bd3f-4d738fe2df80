import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ToastModule} from "primeng/toast";
import {ButtonDirective, ButtonModule} from "primeng/button";
import {TableModule} from "primeng/table";
import {itemModel} from "../../interfaces/items";
import {ActivatedRoute, Router} from "@angular/router";
import {DomSanitizer} from "@angular/platform-browser";
import {AchBatchService} from "../../Service/achbatch.service";
import {CurrencyPipe, NgStyle} from "@angular/common";
import {Ripple, RippleModule} from "primeng/ripple";
import {DialogModule} from "primeng/dialog";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {MessageService} from "primeng/api";
import {GenericService} from "../../Service/generic.service";
import {RespMessage} from "../../interfaces/respmesg";

@Component({
  selector: 'app-achitems',
  standalone: true,
  imports: [
    ToastModule,
    TableModule,
    CurrencyPipe,
    NgStyle,
    DialogModule,
    ProgressSpinnerModule,
    ButtonModule,
    RippleModule,
  ],
  templateUrl: './achitems.component.html',
  styleUrl: './achitems.component.css'
})
export class AchitemsComponent implements OnInit, OnDestroy {
  httpRslt: any
  respMsg!: RespMessage;
  nchItems!: itemModel[];
  selectedItem!: itemModel;
  batchRecKey:number=0;
  achEffDate:Date | undefined;
  showSpinnerDialog: boolean = false;

  constructor(private route: ActivatedRoute,
              private achBatchService: AchBatchService,
              private genericService: GenericService,
              private messageService: MessageService,
              private router: Router) { }

  ngOnInit(): void {
    this.batchRecKey = this.route.snapshot.params['batchRecKey'];
    this.fetchBatchItems(this.batchRecKey.toString())
    //console.log(this.batchRecKey.toString());
  }

  fetchBatchItems(batchRecKey: string) {
     this.achBatchService.fetchBatchItems(batchRecKey).subscribe( items =>{
       this.nchItems = items;
       this.achEffDate = this.nchItems[0].effDate;
       //console.log(this.achEffDate);
     })
  }

  returnToBatchList() {
    this.router.navigateByUrl('/achinbatch').then(r => '/');
  }

  ngOnDestroy(): void {
  }

  batchDetailsReport() {
    //console.log("Batch Record Key :"+this.batchRecKey)
    this.showSpinnerDialog = true
    this.httpRslt = this.genericService.generateReportACHRep05(this.achEffDate?.toString(), this.batchRecKey.toString()).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog = false
      this.messageService.add({
        severity: 'info',
        summary: this.respMsg.message
      });
      //console.log(this.respMsg.message)
    })
  }
}
