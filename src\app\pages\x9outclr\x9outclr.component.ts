import {Component, inject, OnInit} from '@angular/core';
import {MessageService} from "primeng/api";
import {CurrencyPipe, formatDate, NgForOf} from "@angular/common";
import {RippleModule} from "primeng/ripple";
import {ToastModule} from "primeng/toast";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {X9ChqModel} from "../../API/x9cheques";
import {TableHeaderCheckboxToggleEvent, TableModule, TableRowSelectEvent} from "primeng/table";
import {X9chqService} from "../../Service/x9chq.service";
import {ImageModule} from "primeng/image";
import {TabViewModule} from "primeng/tabview";
import {fbsImageModel} from "../../API/fbsImage";
import {DomSanitizer} from "@angular/platform-browser";
import {FbsitemsService} from "../../Service/fbsitems.service";
import {ReportService} from "../../Service/report.service";
import {HttpClient} from "@angular/common/http";
import {CheckboxChangeEvent, CheckboxModule} from "primeng/checkbox";
import {RespMessage} from "../../interfaces/respmesg";
import {DialogModule} from "primeng/dialog";
import {ProgressSpinnerModule} from "primeng/progressspinner";

@Component({
  selector: 'app-x9outclr',
  standalone: true,
  imports: [
    ToastModule,
    CalendarModule,
    FormsModule,
    TableModule,
    CurrencyPipe,
    ImageModule,
    TabViewModule,
    CheckboxModule,
    NgForOf,
    DialogModule,
    ProgressSpinnerModule,
    RippleModule
  ],
  templateUrl: './x9outclr.component.html',
  styleUrl: './x9outclr.component.css'
})

export class X9OutClrComponent implements OnInit{
  x9Chqs!: X9ChqModel[];
  x9ChqsBkup!: X9ChqModel[];
  selectedChq: X9ChqModel = {isSelected: false};
  selectedX9Chq!: X9ChqModel;
  procDate: Date = new Date();
  maxDate: Date = new Date();
  previousDate: Date = new Date;
  selectedDate: Date = new Date;
  dateString: string = '';
  noOfChqs: number = 0;
  itemsChecked: number = 0;
  highlighted: any;
  fbsImage: fbsImageModel = {}
  fbsItemID: string = '';
  fImage?: any;
  rImage?: any;
  httpRslt: any;
  isChecked: any;
  imageId: any;
  respMsg!: RespMessage;
  strFbsIdNos: string[] | undefined;
  showSpinnerDialog: boolean = false;

  constructor(private sanitizer: DomSanitizer,
              private fbsitemsService: FbsitemsService,
              private messageService: MessageService,
              private x9chqService: X9chqService,
              private reportService: ReportService) {

    this.isChecked = [];
    this.x9ChqsBkup= [];
  }


  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.LoadX9Cheques(this.dateString);
  }

  LoadX9Cheques(clringDate: string) {
    this.x9chqService.getX9ChqData(clringDate).subscribe(item => {
      this.x9Chqs = item;

      this.noOfChqs = item?.length || 0;
      this.isChecked = this.x9Chqs;
      if (this.noOfChqs > 0) {
        this.selectedChq = this.x9Chqs[0];
        this.selectedChq.chqStatus = "PENDING"
        if (this.selectedChq.fbsX937Ind === 2) {
          this.selectedChq.chqStatus = "Sent to BACH"
        }
        this.imageId = this.selectedChq.iflFBSItemId
        this.showChqImage(this.imageId)
      }

      if (this.x9ChqsBkup.length > 0 && this.procDate.toDateString() === this.previousDate.toDateString()) {
        //console.log("Searching backup cheques")

        for (let i = 0; i < this.noOfChqs; i++) {
          let idx = this.x9ChqsBkup.findIndex((chq) => chq.fbsItemId == this.x9Chqs[i].fbsItemId &&
          chq.iflRefKey == this.x9Chqs[i].iflRefKey && chq.isSelected === true)
          if(idx >= 0) {
            this.isChecked[i].isSelected = true
            //console.log("Cheque found at index; "+idx+"  "+this.x9ChqsBkup[idx].fbsItemId+", "+this.x9ChqsBkup[idx].isSelected);
          }
        }
      }
      this.messageService.add({ severity: 'info', summary: this.noOfChqs+' X9 Cheques loaded'});
    })
  }

  onDateSelected(event: Date) {
    this.selectedDate = event;
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    if (this.procDate.toDateString() != this.previousDate.toDateString()) {
      this.x9ChqsBkup = [];
      this.LoadX9Cheques(this.dateString);
      this.previousDate = this.procDate;
    } else {
      this.messageService.add({
        severity: 'info',
        "summary": 'Date already selected',
        "detail": this.procDate.toDateString()
      });
    }
  }

  refreshList() {
    this.x9ChqsBkup = this.x9Chqs
    this.LoadX9Cheques(this.dateString)
  }

  generateX9File() {
    this.strFbsIdNos=[];
    //console.log("Items selected "+this.itemsChecked);
    if (this.itemsChecked > 0) {
      for (let i = 0; i < this.x9Chqs.length; i++) {
        if (this.x9Chqs[i].isSelected && this.x9Chqs[i].fbsX937Ind == 1) {
          this.strFbsIdNos.push(String(this.x9Chqs[i].fbsItemId).trim())
          //console.log(i+" "+this.x9Chqs[i].fbsItemId)
        }
          this.x9Chqs[i].isSelected = false

        this.itemsChecked = 0
      }
    } else {
      this.messageService.add({severity: 'info',summary: "No item was selected"})
      //console.log("Nothing selected");
    }

    if (this.strFbsIdNos.length > 0) {
      this.showSpinnerDialog = true
      this.x9ChqsBkup = this.x9Chqs
      this.httpRslt = this.fbsitemsService.generateX9File(this.strFbsIdNos.toString()).subscribe(resp => {
        this.respMsg = resp
        this.showSpinnerDialog = false
        this.messageService.add({
          severity: 'info',
          summary: this.respMsg.message
        })
      });
    }
    this.strFbsIdNos=[];
  }

  selectedRow(chequeItem: any) {
    this.selectedChq = chequeItem
    this.selectedChq.chqStatus = "PENDING"
    if (this.selectedChq.fbsX937Ind === 2) {
      this.selectedChq.chqStatus = "Sent to BACH"
    }
    this.highlighted = chequeItem
    this.fbsItemID = this.highlighted.iflFBSItemId
    //console.log("selectedRow :: Item Selected: "+this.highlighted.iflFBSItemId)
    this.showChqImage(this.fbsItemID)
  }

  showChqImage(iflItemId: string) {
    this.fbsitemsService.getFBSImageData(iflItemId).subscribe(imageData => {
      this.fbsImage = imageData
      this.fImage = this.fbsImage.frontImageData
      let imgURL = 'data:image/png;base64,'+imageData.frontImageData;
      this.fImage = this.sanitizer.bypassSecurityTrustUrl(imgURL);
      this.rImage = this.fbsImage.rearImageData
      let rImgURL = 'data:image/png;base64,'+imageData.rearImageData;
      this.rImage = this.sanitizer.bypassSecurityTrustUrl(rImgURL);
    });
  }

  cbOnClick(chequeItem: any) {
    console.log("cbOnClick :: Item Selected: "+chequeItem.iflRefKey+", "+chequeItem.iflFBSItemId+", "+chequeItem.isSelected+", "+chequeItem.fbsX937Ind);
    if (chequeItem.fbsX937Ind == 2) {
      console.log("cbOnClick X937 = 2 :: chequeItem.isSelected "+chequeItem.isSelected);
      chequeItem.isSelected = false
    }
  }

  OnChangeEvent(chequeItem: any) {
    //console.log("CB OnChangeEvent :: Item Selected: "+chequeItem.iflRefKey+", "+chequeItem.iflFBSItemId+", "+chequeItem.isSelected+", "+chequeItem.fbsX937Ind);
    // ***** The code below is updating the x9Chqs[].isSelected to what the Checkbox has
    // ***** via the chequeItem.isSelected everytime the checkbox is selected or unselected
    let idx = this.x9Chqs.findIndex((item) => item.iflFBSItemId == chequeItem.iflFBSItemId)
    //console.log("Row Index: "+idx)
    if(idx >= 0 && idx < this.isChecked.length) {
      this.x9Chqs[idx].isSelected = chequeItem.isSelected;
      console.log("OnChangeEvent :: chequeItem.isSelected for idx "+idx+" is "+chequeItem.isSelected);
      this.itemsChecked++
    }
  }

  headerCheckboxToggle($event: TableHeaderCheckboxToggleEvent) {
    if(!$event.checked)
      this.itemsChecked = 0

    if (this.x9Chqs.length > 0) {
      for (let i = 0; i < this.x9Chqs.length; i++) {
        //this.isChecked[i].isSelected = $event.checked;
        if (this.x9Chqs[i].fbsX937Ind == 1) {
          this.x9Chqs[i].isSelected = $event.checked;
          if($event.checked)
            this.itemsChecked++
        } else
          this.x9Chqs[i].isSelected = false
      }
      //console.log("Number of checked Items: "+this.itemsChecked)
    }
  }

  printX9OutClrReport1() {
    this.showSpinnerDialog=true
    this.httpRslt = this.reportService.x9OutClrReport1(this.dateString).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog=false
      this.messageService.add({ severity: 'info',
        summary: this.respMsg.message});
    })
  }
  onRowSelect(event: TableRowSelectEvent) {
    this.selectedChq = event.data
    console.log("onRowSelect :: Item Selected: "+this.selectedChq.fbsItemId,)
    //this.messageService.add({ severity: 'info', summary: 'Item marked as du
    this.httpRslt = this.fbsitemsService.updateX9Ind(this.selectedChq.fbsItemId, 1).subscribe(resp => {
      console.log("Item "+this.selectedChq.fbsItemId+" x937Ind changed to 1")
      //this.messageService.add({ severity: 'info', summary: 'Item marked as duplicate', detail: fbsItem.itemID});
      //console.log("Response: "+resp)
    })
  }

  onRowUnselect(event: TableRowSelectEvent) {
    this.selectedChq = event.data
    console.log("onRowUnselect :: Item Unselected: "+this.selectedChq.fbsItemId)
    //this.messageService.add({ severity: 'info', summary: 'Item marked as du
    this.httpRslt = this.fbsitemsService.updateX9Ind(this.selectedChq.fbsItemId, 0).subscribe(resp => {
      console.log("Item "+this.selectedChq.fbsItemId+" x937Ind changed to 0")
      //this.messageService.add({ severity: 'info', summary: 'Item marked as duplicate', detail: fbsItem.itemID});
      //console.log("Response: "+resp)
    })
  }
}
