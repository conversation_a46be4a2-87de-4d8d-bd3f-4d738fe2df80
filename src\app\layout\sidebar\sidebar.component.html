<div
  class="layout-sidebar"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <div class="layout-sidebar-top">
    <a href="/">
      <svg
        width="100"
        viewBox="0 0 64 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="layout-sidebar-logo"
      >
        <path
          d="M3.528 0.399995H7.7L10.892 20H7.812L7.252 16.108V16.164H3.752L3.192 20H0.335999L3.528 0.399995ZM6.888 13.504L5.516 3.816H5.46L4.116 13.504H6.888Z"
          fill="var(--topbar-item-text-color)"
        />
        <path
          d="M10.7813 0.399995H13.8893L15.9053 15.604H15.9613L17.9773 0.399995H20.8053L17.8373 20H13.7493L10.7813 0.399995Z"
          fill="var(--topbar-item-text-color)"
        />
        <path
          d="M23.8717 0.399995H28.0437L31.2358 20H28.1557L27.5957 16.108V16.164H24.0957L23.5357 20H20.6797L23.8717 0.399995ZM27.2317 13.504L25.8597 3.816H25.8037L24.4597 13.504H27.2317Z"
          fill="var(--topbar-item-text-color)"
        />
        <path
          d="M32.73 0.399995H35.81V17.2H40.878V20H32.73V0.399995Z"
          fill="var(--topbar-item-text-color)"
        />
        <path
          d="M46.6977 20.28C45.1857 20.28 44.0283 19.8507 43.2257 18.992C42.423 18.1333 42.0217 16.92 42.0217 15.352V5.048C42.0217 3.48 42.423 2.26666 43.2257 1.408C44.0283 0.549329 45.1857 0.119995 46.6977 0.119995C48.2097 0.119995 49.367 0.549329 50.1697 1.408C50.9723 2.26666 51.3737 3.48 51.3737 5.048V15.352C51.3737 16.92 50.9723 18.1333 50.1697 18.992C49.367 19.8507 48.2097 20.28 46.6977 20.28ZM46.6977 17.48C47.7617 17.48 48.2937 16.836 48.2937 15.548V4.852C48.2937 3.564 47.7617 2.92 46.6977 2.92C45.6337 2.92 45.1017 3.564 45.1017 4.852V15.548C45.1017 16.836 45.6337 17.48 46.6977 17.48Z"
          fill="var(--topbar-item-text-color)"
        />
        <path
          d="M53.4566 0.399995H57.3206L60.3166 12.132H60.3726V0.399995H63.1166V20H59.9526L56.2566 5.692H56.2006V20H53.4566V0.399995Z"
          fill="var(--topbar-item-text-color)"
        />
      </svg>
      <svg
        width="40px"
        height="40px"
        viewBox="0 0 263 237"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        class="layout-sidebar-logo-slim"
      >
        <defs>
          <path
            d="M169.875092,0 L201.802552,40.512 L211.671532,37.5017474 L262.485555,97 L215.236355,97 L215.102552,96.716 L12,96.7160489 L12.3755516,96.338 L0.802551646,96.3382282 L56.3339573,31.7052721 L72.7945516,35.582 L100.963444,7.25787078 L129.081709,32.4361037 L169.875092,0 Z"
            id="path-1"
          ></path>
        </defs>
        <g
          id="Avalon-Theme"
          stroke="none"
          stroke-width="1"
          fill="none"
          fill-rule="evenodd"
        >
          <g
            id="logo-type---1"
            transform="translate(-589.000000, -88.000000)"
          >
            <g
              id="avalon-logo-icon"
              transform="translate(589.000000, 88.000000)"
            >
              <g
                id="sword"
                transform="translate(128.894444, 110.300036) rotate(-225.000000) translate(-128.894444, -110.300036) translate(51.394444, 32.800036)"
                fill="var(--topbar-item-text-color)"
                fill-rule="nonzero"
              >
                <path
                  d="M153.271758,1.72854266 C152.491532,0.945909846 151.35909,0.621667658 150.282594,0.870714533 L126.700141,6.30342547 C126.112715,6.43967938 125.577024,6.73594891 125.149313,7.1612536 L44.8073323,87.5056403 L35.7324612,78.4304684 C35.1308987,77.8292067 34.3157816,77.4908278 33.4648714,77.4908278 C32.6139612,77.4908278 31.7988441,77.8292067 31.1972816,78.4304684 L24.3900003,85.2356442 C23.1375472,86.4905036 23.1375472,88.520777 24.3900003,89.7708239 L38.0024573,103.385386 L12.2414456,129.143691 C9.11632844,129.341004 6.20717219,130.642785 3.9723675,132.875183 C1.54987531,135.300082 0.215008123,138.524156 0.215008123,141.950355 C0.215008123,145.376554 1.54987531,148.599726 3.9723675,151.025226 C6.39726594,153.450125 9.6189339,154.784992 13.0472386,154.784992 C16.4755433,154.784992 19.6972112,153.450125 22.1221097,151.025226 C24.4176722,148.731769 25.6562894,145.775992 25.8463831,142.763367 L40.2694456,128.340004 C40.2694456,128.340004 40.2694456,128.340004 40.2718519,128.335191 L49.3443167,119.265433 L49.3443167,119.265433 L51.6119066,117.00025 L65.2243636,130.61 C65.8259261,131.211562 66.6410433,131.549941 67.4940589,131.549941 C68.3446683,131.549941 69.1600862,131.211562 69.7616487,130.61 L76.5668245,123.804824 C77.8216839,122.550566 77.8216839,120.51548 76.5668245,119.265433 L67.4940589,110.190261 L147.838746,29.8482809 C148.261344,29.4229763 148.562125,28.8848786 148.693867,28.2998591 L154.126578,4.71710516 C154.380739,3.63880438 154.051985,2.50876922 153.271758,1.72854266 Z M36.6721019,122.865484 L27.599637,122.865484 L30.2570394,120.205375 L39.329805,120.205375 L36.6721019,122.865484 Z M22.1224105,132.875183 C21.3469964,132.099769 20.486762,131.437148 19.5681761,130.891832 L21.1824691,129.279945 L30.2573402,129.279945 L24.1033558,135.431824 C23.5649573,134.520156 22.9047425,133.660222 22.1224105,132.875183 Z M17.5848245,146.485535 C16.3723753,147.697984 14.7628948,148.365418 13.0475394,148.365418 C11.3345902,148.365418 9.72240265,147.697984 8.51025422,146.485535 C7.30021125,145.277597 6.6303714,143.665711 6.6303714,141.950355 C6.6303714,140.235 7.30021125,138.627625 8.51025422,137.415175 C9.72270344,136.202726 11.3345902,135.535293 13.0475394,135.535293 C14.7628948,135.535293 16.3723753,136.202726 17.5848245,137.415175 C20.0873245,139.91557 20.0873245,143.985441 17.5848245,146.485535 Z M45.7472737,113.7858 L36.6724027,113.7858 L42.5397425,107.920566 L47.0773284,112.460558 L45.7472737,113.7858 Z M67.4943597,123.804824 L53.8819027,110.190261 L53.8819027,110.190261 L53.8819027,110.190261 L31.1969808,87.5053395 L33.4645706,85.2353434 L42.5394417,94.3105153 L60.6867777,112.460258 C60.6867777,112.460258 60.6867777,112.460258 60.6891839,112.460258 L69.7619495,121.535429 L67.4943597,123.804824 Z M142.650871,25.9618864 L62.9570745,105.655082 L58.4197894,101.11509 L114.274567,45.2654255 C115.524614,44.0129723 115.524614,41.9802927 114.274567,40.7281403 C113.014895,39.4756872 110.984621,39.4756872 109.734575,40.7281403 L53.8822034,96.5805114 L49.3449183,92.0453317 L129.040821,12.3494294 L146.725555,8.27504656 L142.650871,25.9618864 Z"
                  id="Shape"
                ></path>
                <path
                  d="M13.0472386,138.740418 C11.2753362,138.740418 9.83970734,140.178453 9.83970734,141.950355 C9.83970734,143.722258 11.2753362,145.160293 13.0472386,145.160293 C14.8191409,145.160293 16.2568753,143.722258 16.2568753,141.950355 C16.2568753,140.178453 14.8191409,138.740418 13.0472386,138.740418 Z"
                  id="Shape"
                ></path>
              </g>
              <g
                id="avalon-mountain"
                transform="translate(0.000000, 140.000000)"
              >
                <mask
                  id="mask-2"
                  fill="var(--topbar-item-text-color)"
                >
                  <use xlink:href="#path-1"></use>
                </mask>
                <use
                  id="Path-2"
                  fill="var(--topbar-item-text-color)"
                  xlink:href="#path-1"
                ></use>
                <polygon
                  id="Path-5"
                  fill="var(--topbar-item-text-color)"
                  mask="url(#mask-2)"
                  points="147.740772 17.6843628 157.629736 25.3076051 165.776027 18.6830912 172.652346 23.82668 178.491529 18.6830912 186.209417 20.6029101 169.991156 -0.102425566"
                ></polygon>
                <polygon
                  id="Path-3"
                  fill="var(--topbar-item-text-color)"
                  mask="url(#mask-2)"
                  points="72.6840001 35.3291624 87.0657753 30.2395109 95.2943221 38.9723853 105.37005 29.6689499 114.743615 35.823044 123.277473 27.0070742 101.000282 6.9765569"
                ></polygon>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </a>
    <button
      class="layout-sidebar-anchor p-link"
      type="button"
      (click)="anchor()"
    ></button>

  </div>
  <!--@if (menuProfilePosition === 'start') {
    <app-menu-profile
      #menuProfileStartr
    ></app-menu-profile>
  }-->
  <div #menuContainer class="layout-menu-container">
    <app-menu></app-menu>
  </div>
  <!--@if (menuProfilePosition === 'end') {
    <app-menu-profile
      #menuProfileEnd
    ></app-menu-profile>
  }-->
</div>

