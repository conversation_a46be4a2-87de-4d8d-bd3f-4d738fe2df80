<h4 class="font-bold flex justify-content-center mb-2">Duplicate Cheques</h4>
<p-toast/>
<div class="grid">
  <div class="col-6">
    <div class="card bg-blue-100" style="height:610px">
      <div class="col-3 pt-0" style="padding:0">
        <label class="font-bold block"> Processing Date </label>
      </div >
      <div class="field grid" style="margin-bottom: 0">
        <div class="col-fixed mt-1" style="padding:0; width: 140px">
          <p-calendar [(ngModel)]="procDate"
                      [showIcon]="true"
                      [maxDate]="maxDate"
                      dateFormat="dd M, yy"
                      class="font-bold block justify-content-center mb-2"
                      (onSelect)="onDateSelected($event)">
          </p-calendar>
        </div>
        <div class="col-fixed mt-1 ml-3" style="padding:0; width: 240px">
          <button pButton label="Mark Checked Rows as Duplicate"
                  style="margin-left:5px; width: 250px" (click)="markDupFBSItem()"></button>
        </div>
        <div class="col-fixed mt-1 ml-3" style="padding:0; width: 140px">
          <button pButton label="Refresh List"
                  style="margin-left:25px; width: 150px" (click)="refreshDupList()"></button>
        </div>
      </div>
      <p-table [value]="dupChqs"
               dataKey="id"
               [(selection)]="selectedChq"
               [expandedRowKeys]="expandedRows"
               (onRowExpand)="onRowExpand($event)"
               [tableStyle]="{'min-width': '35rem'}"
               [scrollable]="true"
               scrollHeight= "516px"
               styleClass="p-datatable-sm p-datatable-gridlines">
        <ng-template  pTemplate="header">
          <tr>
            <th class= "bg text-white" style="width: 2rem; height:.5rem"></th>
            <th class= "bg-black-alpha-70 text-white" style="width: 70px">Bank No</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 85px">Chq AC No.</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 85px">Chq Ser No.</th>
            <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 100px">Chq Amount</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 80px; text-align: center;">Dup Count</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-dupChqItem let-expanded="expanded">
          <!--tr [pSelectableRow]="dupChqItem"-->
          <tr>
            <td>
              <button type="button" pButton pRipple
                      class="p-button-text p-button-rounded p-button-plain"
                      [pRowToggler]="dupChqItem"
                      (click)="buttonClick()"
                      [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'">
              </button>
            </td>
            <td style="height:25px">{{dupChqItem.bankNo}}</td>
            <td style="height:25px">{{dupChqItem.chqAcNo}}</td>
            <td style="height:25px">{{dupChqItem.serNo}}</td>
            <td style="text-align: right; height:25px" inputmode="decimal">{{dupChqItem.chqAmount | currency}}</td>
            <td style="text-align: center; height:25px">{{dupChqItem.cnt}}</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="rowexpansion" let-fbsItem>
          <tr>
            <td colspan="6">
              <div class="pt-3 pb-3">
                <p-table [value]="dupFBSItems"
                         dataKey="itemID"
                         selectionMode="single"
                         [(selection)]="selectedItem"
                         (onRowSelect)="onRowSelect($event)"
                         (onRowUnselect)="onRowUnselect($event)"
                         styleClass="p-datatable-sm p-datatable-gridlines">
                  <ng-template pTemplate="header">
                    <tr>
                      <!--th style="width: 2rem">
                        <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                      </th-->
                      <th class= "bg-black-alpha-50 text-white" style="width: 2rem"></th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Item ID</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Route No.</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">AC No.</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Chq No.</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="text-align: right; width: 100px">Chq Amount</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 80px">Chq Date</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-dupFBSItem>
                    <tr [class.ui-state-highlight]="dupFBSItem === highlighted">
                      <td style="width: 2rem">
                        <p-tableCheckbox [value]="dupFBSItem" (click)="cbOnClick(dupFBSItem)"></p-tableCheckbox>
                      </td>
                      <td> <a (click)="selectedRowItem(dupFBSItem)">{{ dupFBSItem.itemID }}</a></td>
                      <td>{{ dupFBSItem.routeNo }}</td>
                      <td>{{ dupFBSItem.chqAcNo }}</td>
                      <td>{{ dupFBSItem.serNo }}</td>
                      <td style="text-align: right" inputmode="decimal">{{dupFBSItem.chqAmount | currency}}</td>
                      <td>{{ dupFBSItem.chqDate }}</td>
                    </tr>
                  </ng-template>
                  <!--ng-template pTemplate="emptymessage">
                    <tr>
                      <td colspan="6">There are no records found</td>
                    </tr>
                  </ng-template-->
                </p-table>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="col-6">
    <div class="card bg-blue-100" style="height:610px">
      <div class="field grid" style="margin-bottom: 0">
        <div class="col-fixed ml-2" style="width:180px; font-weight: bold">Batch Id</div>
        <div class="col-fixed" style="width:250px; font-weight: bold">Item Id</div>
        <div class="col-fixed" style="width:145px; font-weight: bold">Batch Scan Date</div>
        <div class="col-fixed" style="width:125px; font-weight: bold">Processing Date</div>
        <!--div class="col-3 pt-0">
          <label class="font-bold block" style="width:80px"> Batch Id </label>
        </div-->
      </div>
      <div class="field grid">
        <input type="text" pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedFbsItem.batchID" style="margin-left:10px; width:120px; height:25px"/>
        <input pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedFbsItem.itemID" style="margin-left:60px; width:150px; height:25px"/>
        <input pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedFbsItem.fbsDate" style="margin-left:95px; width:120px; height:25px"/>
        <input pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedFbsItem.procDate" style="margin-left:25px; width:120px; height:25px"/>
      </div>

      <div class="card p-fluid" style="margin-top: 20px; margin-bottom: .25rem; padding-top: .5rem; padding-bottom: .5rem">
        <div class="field grid" style="margin-bottom: 0;margin-top: 15px">
          <div class="col-fixed" style="width:80px; font-weight: bold">Bank No</div>
          <div class="col-fixed" style="width:130px; font-weight: bold">Routing No.</div>
          <div class="col-fixed" style="width:145px; font-weight: bold">Account No.</div>
          <div class="col-fixed" style="width:125px; font-weight: bold">CHQ Date</div>
          <div class="col-fixed" style="width:100px; font-weight: bold">Serial No.</div>
          <div class="col-fixed" style="width:120px; font-weight: bold; text-align:right">Amount</div>
        </div>

        <div class="field grid">
          <input pInputText class="p-inputtext" style="margin-left:5px; width:50px; height:25px" [(ngModel)]="selectedFbsItem.bankNo"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:100px; height:25px" [(ngModel)]="selectedFbsItem.routeNo"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:110px; height:25px" [(ngModel)]="selectedFbsItem.chqAcNo"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:100px; height:25px" [disabled]=true [(ngModel)]="selectedFbsItem.chqDate"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:85px; height:25px" [(ngModel)]="selectedFbsItem.serNo"/>
          <p-inputNumber class="p-inputnumber-1" [disabled]=true [(ngModel)]="selectedFbsItem.chqAmount" mode="decimal" [minFractionDigits]="2"></p-inputNumber>
          <!--input pInputNumber style="margin-left:30px; width:100px; height:25px; text-align:right" [(ngModel)]="selectedFbsItem.chqAmount" /-->
        </div>

        <div class="field grid" style="margin-bottom: 0; margin-top: 20px">
          <div class="col-fixed" style="width:230px; font-weight: bold">Beneficiary A/C No.</div>
          <div class="col-fixed" style="width:230px; font-weight: bold">MICR - On Us</div>
          <div class="col-fixed" style="width:150px; font-weight: bold">MICR - Aux On Us</div>
        </div>

        <div class="field grid">
          <input pInputText class="p-inputtext" style="margin-left:5px; width:150px; height:25px" [(ngModel)]="selectedFbsItem.custAcNo"/>
          <input pInputText class="p-inputtext" style="margin-left:75px; width:200px; height:25px" [disabled]=true [(ngModel)]="selectedFbsItem.onUs"/>
          <input pInputText class="p-inputtext" style="margin-left:35px; width:235px; height:25px" [disabled]=true [(ngModel)]="selectedFbsItem.auxOnUs"/>
        </div>
      </div>
      <div class="col-12">
        <button pButton label="Save Changes" icon="pi pi-chevron-left"
                (click)="saveFBSItem()" style="margin-left:5px; width: 150px"></button>
      </div>
      <div class="col-12" style="padding: .25rem 0 0 0">
        <p-tabView>
          <p-tabPanel header="Chq Fromt Image">
            <p-image [src]='fImage' alt="Front Image" height = "230px" width="650px" [preview] = "true"></p-image>
          </p-tabPanel>
          <p-tabPanel header="Chq Rear Image">
            <p-image [src]='rImage' alt="Rear Image" height= 230px width="650px" [preview] = "true" previewImageSizes="40%"></p-image>
          </p-tabPanel>
        </p-tabView>
      </div>
    </div>
  </div>
</div>
