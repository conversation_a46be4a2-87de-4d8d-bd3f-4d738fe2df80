import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {itemModel} from "../interfaces/items";
import {environment} from "../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class GenericService {

  constructor(private http: HttpClient) {

  }

  putMarkDupItem(itemId: any) {
    //console.log("Http Put, itemId:  "+itemId)
    const body = JSON.stringify({itemid:itemId.trim(),itmStatus:99})
    //console.log("Body" +body)
    return this.http.put<any>(environment.baseUrl+'fbsItem/markDuplicate', body);
  }

  updateFBSChqDet(itemData: itemModel) {
    //console.log(fbsItemData)
    //console.log("Http Put, itemId:  "+itemId)
    const body = JSON.stringify(itemData)
    //console.log("Body" +body)
    return this.http.put<any>(environment.baseUrl+'fbsItem/updateFBSChqDet', body);
  }

  updateX9Ind(itemId: any, x9Ind: number) {
    const body = JSON.stringify({itemid:itemId.trim(),x937Ind:x9Ind})
    //console.log("Body" +body)
    return this.http.put<any>(environment.baseUrl+'fbsItem/updateX937Ind', body);
  }

  generateX9File(itemId: any, x9Ind: number) {
    const body = JSON.stringify({itemid:itemId.trim(),x937Ind:x9Ind})
    return this.http.put<any>(environment.baseUrl+'outClr/generateX9File', body);
  }

  copyCruiseFile(fileName: string | undefined, fileDate: string) {
    const body = JSON.stringify({fileName: fileName, fileDate: fileDate})
    return this.http.put<any>(environment.baseUrl + 'copyCruiseACHFile', body);

  }

  printCruiseFile(fileName: string | undefined, fileDate: string) {
    if (fileName === undefined) {
      fileName = "";
    }
    const body = JSON.stringify({fileName:fileName, fileDate:fileDate})
    return this.http.put<any>(environment.baseUrl+'printCruiseACHFile', body);
}

  generateReturnFile(achDate: string, retDate: string) {
    const body = JSON.stringify({achDate:achDate, retDate:retDate})
    return this.http.put<any>(environment.baseUrl+'genrateReturnFile', body);
  }

  generateHostACHFile(fileDate: string) {
    return this.http.put<any>(environment.baseUrl+'genrateCruiseACHFile', fileDate);
  }

  generateCruiseFile(recIdNos: string, achDate: string) {
    const reqBody = JSON.stringify({recIdNos: recIdNos, effDate:achDate})
    return this.http.put<any>(environment.baseUrl+'genrateCruiseACHFile', reqBody);
  }

  generateEPayACHFile(recIdNos: string, achDate: string, fileOutput: string) {
    const reqBody = JSON.stringify({recIdNos: recIdNos, effDate:achDate, fileOutput: fileOutput})
    //console.log(reqBody);
    return this.http.put<any>(environment.baseUrl+'genrateEPayACHFile', reqBody);
  }

  generateReportRetRep01(repdate: string) {
    return this.http.put<any>(environment.baseUrl+'generateReportRetRep01', repdate);
  }

  generateReportRetRep02(repdate: string) {
    return this.http.put<any>(environment.baseUrl+'generateReportRetRep02', repdate);
  }

  generateReportACHRep01(repdate: string) {
    return this.http.put<any>(environment.baseUrl+'generateReportACHRep01', repdate);
  }

  generateReportACHRep02(repdate: string, fileType: string) {
    const reqBody = JSON.stringify({repDate: repdate, fileType: fileType});
    //console.log(reqBody);
    return this.http.put<any>(environment.baseUrl+'generateReportACHRep02', reqBody);
  }

  generateReportACHRep03(repDate: string, recidnos: string) {
    const reqBody = JSON.stringify({repDate: repDate, recIdNos: recidnos});
    //console.log(reqBody);
    //console.log(recidnos)
    //console.log(recidnos.toString())
    return this.http.put<any>(environment.baseUrl+'generateReportACHRep03', reqBody);
  }

  generateReportACHRep04(recidnos: string, fileType: string, repDate: string) {
    const reqBody = JSON.stringify({repDate: repDate, recIdNos: recidnos, fileType: fileType});
    // console.log(reqBody)
    //console.log(recidnos.toString())
    //return this.http.post<any>(environment.baseUrl+'reports/ACHRep04',  reqBody);
    return this.http.put<any>(environment.baseUrl+'generateReportACHRep04', reqBody);
  }

  generateReportACHRep05(repDate: string | undefined, batchRecKey: string) {
    const reqBody = JSON.stringify({repDate: repDate, batchRecId: batchRecKey});
    //console.log(reqBody);
    return this.http.put<any>(environment.baseUrl+'generateReportACHRep05', reqBody);
  }
}


