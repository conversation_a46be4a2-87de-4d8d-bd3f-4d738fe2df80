import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/useToast';
import { useIdleTimer } from '../hooks/useIdleTimer';

export const LoginComponent: React.FC = () => {
  const [userName, setUserName] = useState<string>('');
  const [userPassword, setUserPassword] = useState<string>('');
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  
  const navigate = useNavigate();
  const { login, isAuthenticated } = useAuth();
  const { toast, showInfo } = useToast();
  const { startTimer } = useIdleTimer();

  const handleLogin = async () => {
    const user = await login(userName, userPassword);
    
    if (isAuthenticated && user) {
      startTimer(10); // 10 minutes
      navigate('/dashboard');
    } else {
      showInfo('Login Details', 'Invalid Credentials!');
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleLogin();
    }
  };

  return (
    <div className="overflow-hidden margin-0 relative h-screen">
      <div 
        className="bg-cover bg-center" 
        style={{ 
          background: "url('assets/layout/images/login/bg-login.jpg')", 
          height: 'calc(100% - 370px)' 
        }}
      />
      <div className="w-full absolute mb-0 bottom-0 text-center surface-900 border-noround p-fluid h-27rem">
        <div className="px-6 md:p-0 w-29rem relative text-white" style={{ marginLeft: '-200px', top: '30px', left: '50%' }}>
          <div className="grid">
            <div className="col-3 text-left">
              <img src="assets/layout/images/login/icon-login.svg" alt="bob-img-sol" />
            </div>
            <div className="col-9 text-left">
              <h2 className="mb-0 text-0">User Sign In</h2>
              <span className="text-500 text-sm">BOB Image Solutions</span>
            </div>
            <div className="col-12 text-left">
              <label className="text-400 mb-1">Username</label>
              <div className="mt-1">
                <InputText
                  type="text"
                  placeholder="Username"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  onKeyPress={handleKeyPress}
                  required
                />
              </div>
            </div>
            <div className="col-12 text-left">
              <label className="text-400 mb-1">Password</label>
              <div className="mt-1">
                <Password
                  value={userPassword}
                  onChange={(e) => setUserPassword(e.target.value)}
                  onKeyPress={handleKeyPress}
                  feedback={false}
                  placeholder="Password"
                  required
                />
              </div>
            </div>
            <div className="col-12 md:col-6">
              <Button
                type="button"
                onClick={handleLogin}
                label="Sign In"
              />
            </div>
          </div>
        </div>
      </div>
      <Toast ref={toast} />
    </div>
  );
};
