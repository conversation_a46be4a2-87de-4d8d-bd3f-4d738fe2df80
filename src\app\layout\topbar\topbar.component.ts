import {Component, ElementRef, ViewChild} from '@angular/core';
import {StyleClassModule} from "primeng/styleclass";
import {NgClass} from "@angular/common";
import {RouterLink} from "@angular/router";
import {RippleModule} from "primeng/ripple";
import {LayoutService} from "../layout.service";

@Component({
  selector: 'app-topbar',
  standalone: true,
  imports: [NgClass, RouterLink, RippleModule, StyleClassModule],
  templateUrl: './topbar.component.html',
})
export class TopbarComponent {
  @ViewChild('menuButton') menuButton!: ElementRef;

  constructor(public layoutService: LayoutService, public el: ElementRef) {}

  activeItem!: number;
  get mobileTopbarActive(): boolean {
    return this.layoutService.state.topbarMenuActive;
  }

  onMenuButtonClick() {
    this.layoutService.onMenuToggle();
  }

  onMobileTopbarMenuButtonClick() {
    this.layoutService.onTopbarMenuToggle();
  }
}
