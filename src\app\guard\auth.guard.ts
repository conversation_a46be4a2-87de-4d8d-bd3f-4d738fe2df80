import { CanActivateFn,  Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import {inject} from "@angular/core";
//import {session} from "../session";
import {AuthService} from "../Service/auth.service";

// @ts-ignore
export const CanActivate = () => {
  const authService = inject(AuthService);
  const router:Router = inject(Router);
  if (authService.IsAuthenticated()) {
     return true;
  }else {
     //return true;
     router.navigate(['/']).then(r => '/');
  }
}

// export const authGuard = (state: RouterStateSnapshot, route: ActivatedRouteSnapshot) => {
//   const router:Router = inject(Router);
//   const protectedPages: string[] = ['achinbatch']
//   console.log(state.url.toString())
//   const url: boolean = protectedPages.includes(state.url.toString())
//   console.log(url +" && "+ session)
//   return !session ? router.navigate(['/']) : true
//   //return url && !session ? router.navigate(['/']) : true
//   //return false;
// };
