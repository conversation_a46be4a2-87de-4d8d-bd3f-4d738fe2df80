import {Component, OnInit} from '@angular/core';
import {ToastModule} from "primeng/toast";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {TableModule, TableRowExpandEvent, TableRowSelectEvent, TableRowUnSelectEvent} from "primeng/table";
import {iflUnmatchModel} from "../../API/ifltransmodel";
import {ExpandedRows} from "../../API/expandedrows";
import {IfltransService} from "../../Service/ifltrans.service";
import {Router} from "@angular/router";
import {MessageService} from "primeng/api";
import {CurrencyPipe, formatDate} from "@angular/common";
import {RippleModule} from "primeng/ripple";
import {fbsItemModel} from "../../API/fbsItem";
import {HttpParams} from "@angular/common/http";
import {FbsitemsService} from "../../Service/fbsitems.service";
import {DomSanitizer} from "@angular/platform-browser";
import {InputTextModule} from "primeng/inputtext";
import {fbsImageModel} from "../../API/fbsImage";
import {InputNumberModule} from "primeng/inputnumber";
import {ImageModule} from "primeng/image";
import {TabViewModule} from "primeng/tabview";

@Component({
  selector: 'app-unmatchitems',
  standalone: true,
  imports: [
    ToastModule,
    CalendarModule,
    FormsModule,
    TableModule,
    CurrencyPipe,
    RippleModule,
    InputTextModule,
    InputNumberModule,
    ImageModule,
    TabViewModule
  ],
  templateUrl: './unmatchitems.component.html',
  styleUrl: './unmatchitems.component.css'
})


export class UnmatchitemsComponent implements OnInit{
  procDate: Date = new Date;
  maxDate: Date = new Date();
  fcTrans!: iflUnmatchModel[];
  selectedTran!: iflUnmatchModel;
  expandedRows: ExpandedRows = {};
  previousDate: Date = new Date;
  selectedDate: Date = new Date;
  dateString: string = '';
  noOfTrans: number = 0;
  isExpanded: boolean = false;
  UMFBSItems!: fbsItemModel[];
  selectedItem: fbsItemModel = {
    selected: false
  };
  fbsImage: fbsImageModel = {}
  iflRefNo: any;
  batchId: any;
  itemId: any;
  fImage?: any;
  rImage?: any;
  selectedChqItem: fbsItemModel = {
    selected: false
  };
  fbsItemId: any;


  constructor(private iflTransService: IfltransService,
              private fbsitemsService: FbsitemsService,
              private sanitizer: DomSanitizer,
              private messageService: MessageService) {
  }

  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.LoadIflData(this.dateString);
  }

  onDateSelected(event: Date) {
    this.selectedDate = event;
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    if (this.procDate.toDateString() != this.previousDate.toDateString()) {
      this.LoadIflData(this.dateString);
      this.previousDate = this.procDate;
    } else {
      this.messageService.add({
        severity: 'info',
        "summary": 'Date already selected',
        "detail": this.procDate.toDateString()
      });
      console.log("Date selected... [" + this.selectedDate.toDateString() + "]")
    }
  }

  onRowExpand(event: TableRowExpandEvent) {
    //console.log("onRowExpand....this.isExpanded before value = "+this.isExpanded)
    this.selectedTran = event.data;
    this.iflRefNo = event.data.iflRefNo;
    this.fbsItemId = event.data.itemId
    //console.log("FBS ItemId: "+this.fbsItemId+" is expanding, IFLRefNo: "+this.iflRefNo)
    this.fbsitemsService.getFbsItem(this.fbsItemId).subscribe(item => {
      this.UMFBSItems = item;
      //this.selectedItem = item[0];
      //console.log(this.UMFBSItems)       //Display the array data....
    })
  }

  buttonClick() {
    //console.log("buttonClick :: isExpanded value at Start: "+this.isExpanded)
    if (this.isExpanded) {
      //console.log("Clasping  :: isExpanded: "+this.isExpanded)
      this.expandedRows = {};
      this.selectedItem = {selected: false};
      this.rImage = '';
      this.fImage = '';
      //this.fbsImage = {};
    } else {
      //console.log("Expanding :: isExpanded: "+this.isExpanded)
      //this.expandedRows = {};
    }
    this.isExpanded = !this.isExpanded;
    //console.log("buttonClick end :: isExpanded changed to: "+this.isExpanded)
  }

  LoadIflData(fbsDate: string) {
    //debugger
    this.iflTransService.getIflTransData(fbsDate).subscribe(item => {
      //console.log(item)
      this.noOfTrans = item.length;
      this.fcTrans = item;
      this.messageService.add({ severity: 'info',
        summary: this.noOfTrans+' Entries loaded'});
      //console.log(this.fcTrans);
    })
  }

  onRowSelect(event: TableRowSelectEvent) {
    this.selectedItem = event.data
    //console.log("OnRowSelect: "+this.selectedItem.itemID)
    this.itemId = this.selectedItem.itemID;
    //console.log(this.itemId)
    this.fbsitemsService.getFBSImageData(this.itemId).subscribe(item => {
      this.fbsImage = item
      this.fImage = this.fbsImage.frontImageData
      let fImgURL = 'data:image/png;base64,' + item.frontImageData;
      this.fImage = this.sanitizer.bypassSecurityTrustUrl(fImgURL);

      this.rImage = this.fbsImage.rearImageData
      let rImgURL = 'data:image/png;base64,' + item.rearImageData;
      this.rImage = this.sanitizer.bypassSecurityTrustUrl(rImgURL);
    });
  }

  onRowUnselect(event: TableRowUnSelectEvent) {
    this.selectedItem = {selected: false};
    this.rImage = '';
    this.fImage = '';
    //this.fbsImage = {};
    console.log("OnRowUnselect: "+this.selectedItem.itemID)
  }

  cbOnClick(dupFBSItem: any) {

  }

  selectedRowItem(UnmatchFBSItem: any) {

  }

  saveFBSItem() {
    //console.log("Save FBS Item...")
    //console.log(this.selectedItem)
    this.fbsitemsService.updateFBSChqDet(this.selectedItem).subscribe(data => {
      this.messageService.add({ severity: 'info', summary: 'Changes Saved', detail: this.selectedItem.itemID});
      //console.log("Response: "+data)
    })
  }

  updateMatchFBSItem() {
    console.log("Update Matched Items :: IFLRefKey: "+this.iflRefNo+", FBS ItemId: "+this.fbsItemId);
    this.iflTransService.updateIflTrans(this.iflRefNo, this.fbsItemId).subscribe( data => {
      this.messageService.add({ severity: 'info', summary: 'Item Matched', detail: this.iflRefNo+" : "+this.fbsItemId});
    })
  }

  refreshList() {

  }
}
