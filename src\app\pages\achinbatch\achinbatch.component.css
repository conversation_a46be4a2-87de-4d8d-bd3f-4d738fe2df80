.p-calendar .p-inputtext {
  font-family: var(--font-family), serif;
  font-feature-settings: var(--font-feature-settings, normal);
  font-size: 20px;
  color: #212529;
  background: #b2e3eb;
  padding: 0.5rem 0.75rem;
  border: 2px solid #ced4da;
  transition: background-color 0.15s, border-color 0.15s, box-shadow 0.15s;
  appearance: none;
  border-radius: 5px;
}

:host ::ng-deep .p-inputtext-sm .p-inputtext {
  font-size: 17px;
  width: 135px;
  height: 35px;
  font-weight: bold;
  padding: 0.4375rem 0.65625rem;
}

:host ::ng-deep .p-input-filled .p-inputtext {
  background-color: #b2e3eb;
}

h4 {
  color: #073d8b;
  margin: 0 0 0 0;
}
.ui-state-highlight {
  background: lightblue;
}


/*:host ::ng-deep .p-calendar .p-datepicker table td.p-datepicker-today>span,
.p-calendar .p-datepicker table td.p-datepicker-today>span:hover {
  background-color: lightblue;
  color: white;
  font-weight: bold;
}*/
