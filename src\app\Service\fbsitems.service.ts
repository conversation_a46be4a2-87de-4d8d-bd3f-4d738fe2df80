import { Injectable } from '@angular/core';
import {HttpClient, HttpParams} from "@angular/common/http";
import {fbsItemModel} from "../API/fbsItem";
import {fbsImageModel} from "../API/fbsImage";


@Injectable({
  providedIn: 'root'
})
export class FbsitemsService {
  constructor(private http: HttpClient) { }

  getFBSItemData(batchId: string) {
    return this.http.get<fbsItemModel[]>('http://localhost:8080/api/fbsitems/'+batchId);
  }

  getFBSImageData(itemId: string) {
    return this.http.get<fbsImageModel>('http://localhost:8080/api/fbsImage/'+itemId);
    //return this.http.get('http://localhost:8080/files/'+itemId);
  }

  getFBSDupData(dupChq: HttpParams) {
    return this.http.get<fbsItemModel[]>('http://localhost:8080/api/dupitems/'+dupChq);
  }

  getFbsChqData(iflChqData: HttpParams) {
    return this.http.get<fbsItemModel[]>('http://localhost:8080/api/fbsmatchItem/'+iflChqData);
  }

  getFbsItem(itemId: string) {
    return this.http.get<fbsItemModel[]>('http://localhost:8080/api/fbsMatchedItem/'+itemId);
  }

  putMarkDupItem(itemId: any) {
    //console.log("Http Put, itemId:  "+itemId)
    const body = JSON.stringify({itemid:itemId.trim(),itmStatus:99})
    //console.log("Body" +body)
    return this.http.put<any>('http://localhost:8080/api/fbsItem/markDuplicate', body);
  }

  updateFBSChqDet(fbsItemData: fbsItemModel) {
    //console.log(fbsItemData)
    //console.log("Http Put, itemId:  "+itemId)
    const body = JSON.stringify(fbsItemData)
    //console.log("Body" +body)
    return this.http.put<any>('http://localhost:8080/api/fbsItem/updateFBSChqDet', body);
  }
  updateX9Ind(itemId: any, x9Ind: number) {
    const body = JSON.stringify({fbsIdNos:itemId.trim(),x937Ind:x9Ind})
    //console.log("Body" +body)
    return this.http.put<any>('http://localhost:8080/api/fbsItem/updateX937Ind', body);
  }

  generateX9File(itemIds: string) {
    const body = JSON.stringify({itemIds:itemIds.trim()})
    //console.log(body)
    return this.http.put<any>('http://localhost:8080/api/outClr/generateX9File', body);
  }
}
