import {Component, inject, OnInit} from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { PrimeNGConfig } from "primeng/api";
import {LoginComponent} from "./pages/login/login.component";
import {HttpClient} from "@angular/common/http";

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, LoginComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  private http = inject(HttpClient);

  constructor(private primengConfig: PrimeNGConfig) {
    //this.fetchData();
  }
  fetchData() {
    this.http.get('http://localhost:8080/api/batchList')
      .subscribe((data) => {
        console.log(data)
      })
  }
  ngOnInit(): void {
    //this.primengConfig.ripple = true;
    //this.fetchData();
  }
}
