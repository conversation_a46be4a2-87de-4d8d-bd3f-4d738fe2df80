import {Component, OnInit} from '@angular/core';
import {MenuitemComponent} from "./menuitem.component";

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [MenuitemComponent],
  templateUrl: './menu.component.html',
})
export class MenuComponent implements OnInit {
  menulist: any[] = [];

  ngOnInit() {
    this.menulist = [
      {
        label: 'Home',
        icon: 'pi pi-home',
        //routerLink: ['/dashboard'],
        items: [
         {
           label: 'Dashboard',
           icon: 'pi pi-fw pi-home',
           routerLink: ['/dashboard'],
           //items: [
           //  { label: 'Overview', icon: 'pi pi-fw pi-home', routerLink: ['/dashboard'] },
           //]
         },
        ]
      },
      {
        label: 'FBS Batch Processing',
        icon: 'pi pi-th-large',
        items: [
          {
            label: 'FBS Batch List',
            icon: 'pi pi-fw pi-list',
            routerLink: ['/batches'],
          },
          {
            label: 'X9 Out Clearing',
            icon: 'pi pi-fw pi-calendar',
            routerLink: ['/x9outclr']
          },
          {
            label: 'Duplicate Items',
            icon: 'pi pi-fw pi-list',
            routerLink: ['/dupitems']
          },
          {
            label: 'Unmatched Transactions',
            icon: 'pi pi-fw pi-folder',
            routerLink: ['/unmatchitems']
          },
          {
            label: 'Exception Items',
            icon: 'pi pi-fw pi-list',
            routerLink: ['/']
          },
        ]
      },
      {
        label: 'X9 Processing',
        icon: 'pi pi-fw pi-briefcase',
        items: [
          {
            label: 'X9 In Clearing/Returns',
            icon: 'pi pi-fw pi-pencil',
            routerLink: ['/x9inclr']
          },
          {
            label: 'X9 Out Clearing',
            icon: 'pi pi-fw pi-pencil',
            routerLink: ['/x9outclr']
          }
        ]
      },
      {
        label: 'NACHA Items Processing',
        icon: 'pi pi-th-large',
        items: [
          {
            label: 'Incoming ACH Files',
            icon: 'pi pi-fw pi-folder',
            items: [
              {
                label: 'Batch List',
                icon: 'pi pi-fw pi-list',
                routerLink: ['/achinbatch']
              },
            ]
          },
          {
            label: 'Outgoing ACH Files',
            icon: 'pi pi-fw pi-folder',
            items: [
              {
                label: 'File List',
                icon: 'pi pi-fw pi-list',
                routerLink: ['/achobfiles']
              },
            ]
          },
          {
            label: 'ACH Return Items',
            icon: 'pi pi-fw pi-folder',
            items: [
              {
                label: 'Outgoing Returns',
                icon: 'pi pi-fw pi-list',
                routerLink: ['/rtnitems']
              },
              {
                label: 'Incoming Returns',
                icon: 'pi pi-fw pi-list',
                routerLink: ['/dashboard']
              }
            ]
          },
        ]
      }
    ];
  }
}
