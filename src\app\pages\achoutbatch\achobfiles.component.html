<h4 class = "font-bold flex justify-content-center" >OUTGOING ACH FILES</h4>
<p-toast></p-toast>
<div class="grid">
  <div class="flex-auto">
    <div><label class="font-bold block ml-3 mb-1" style="font-size: 15px">ACH File Date</label></div>
    <div class="inline-flex bottom-0">
      <p-calendar [(ngModel)]="achDate"
                  [showIcon]="true"
                  [maxDate]="maxDate"
                  dateFormat="dd M,  yy"
                  inputId="calendarBtn"
                  class="p-inputtext-sm block ml-3 mb-2"
                  [style]="{'font-size': 'large'}"
                  (onSelect)="onDateSelected($event)">
      </p-calendar>
      <button class ="inline" pButton pRipple label="File List - All Files" icon="pi pi-print" (click)="printFileListReport()"
              style="margin-bottom: 15px; margin-left:20px"></button>

      <button class ="inline" pButton pRipple label="File Details - Selected Files" icon="pi pi-print" (click)="printFileTrans()"
              style="margin-bottom: 15px; margin-left:20px"></button>

      <button class ="inline" pButton pRipple label="Refresh File List" icon="pi pi-refresh" (click)="refreshPage()"
              style="margin-bottom: 15px; margin-left:20px"></button>
    </div>
  </div>
  <div class="col-12 card bg-black-alpha-20">
    <p-table [value]="fileList"
             [(selection)]="selectedFile"
             dataKey="recId"
             selectionMode="single"
             (onRowSelect)="onRowSelect($event)"
             (onRowUnselect)="onRowUnselect($event)"
             [tableStyle]="{'min-width': '50rem'}"
             [scrollable]="true"
             (onHeaderCheckboxToggle)="headerCheckboxToggle($event)"
             scrollHeight="490px"
             styleClass="p-datatable-sm p-datatable-gridlines">
      <ng-template  pTemplate="header">
        <tr>
          <th class= "bg-black-alpha-50 text-white" style="width: 2.5rem">
            <!--p-tableHeaderCheckbox/-->
          </th>
          <th class= "bg-black-alpha-70 text-white" style="width: 6rem">FILE NAME</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 6rem">FILE DATE</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 4rem">BATCHES</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 4rem">ITEMS</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 12rem">ORIGINATOR</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 12rem">DESTINATION</th>
          <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 9rem">CR TOTAL</th>
          <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 9rem">DR TOTAL</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 12rem">PROC DATE & TIME</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 9rem">STATUS</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-fileItem>
        <tr [class.ui-state-highlight]="fileItem === highlighted">
        <!--tr [pSelectableRow]="batchItem"-->
          <td><input type="checkbox"
                     [(ngModel)]="fileItem.isSelected"
                     [value]="fileItem"
                     (click)="cbOnClick(fileItem)"
                     (change)="OnChangeEvent(fileItem)"
                     style="width: 20px; height: 19px; margin-left: 0"/>
          </td>
          <td><a (click)="selectedRow(fileItem)"> {{fileItem.fileName}}</a></td>
          <!--td><a (click)="selectedRow(fileItem)"> {{fileItem.batchId}}</a></td-->
          <td>{{fileItem.fileDate}}</td>
          <td>{{fileItem.batchCount}}</td>
          <td>{{fileItem.itemCount}}</td>
          <td>{{fileItem.origName}}</td>
          <td>{{fileItem.destName}}</td>
          <td style="text-align: right" inputmode="decimal">{{fileItem.creditTotal | currency}}</td>
          <td style="text-align: right" inputmode="decimal">{{fileItem.debitTotal | currency}}</td>
          <td>{{fileItem.procTime | date: "dd-MMM-yyyy hh:mm:ss a"}}</td>
          <td>{{fileItem.fileStatus}}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog header="Processing, pleas wait.."
          [(visible)]="showSpinnerDialog"
          [modal]="true"
          [style]="{ width: '20rem' }"
          position="center">
  <div class="card flex justify-content-center">
    <p-progressSpinner
      styleClass="w-4rem h-4rem"
      strokeWidth="8"
      fill="var(--surface-ground)"
      animationDuration=".5s" />
  </div>
</p-dialog>
