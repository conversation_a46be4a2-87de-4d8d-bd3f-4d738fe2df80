<h4 class = "font-bold flex justify-content-center">BATCH DETAILS - FBS ITEMS</h4>
<button pButton label="Batch List" icon="pi pi-chevron-left" (click)="returnToBatchList()"
        style="margin-bottom: 15px; margin-left:10px"></button>
<div class="grid">
  <div class="col-5 card bg-blue-100" style="height:530px; margin-left: 10px">
        <p-table [value]="fbsItems"
                 dataKey="itemID"
                 selectionMode="single"
                 [(selection)]="selectedItem"
                 (onRowSelect)="onRowSelect($event)"
                 (onRowUnselect)="onRowUnselect($event)"
                 [tableStyle]="{'min-width': '30rem'}"
                 [scrollable]="true"
                 scrollHeight="512px"
                 styleClass="p-datatable-sm p-datatable-gridlines">
          <ng-template pTemplate="header">
            <tr>
              <!--th class="bg-black-alpha-50 text-white" style="width: 4rem"></th-->
              <th class="bg-black-alpha-70 text-white" style="width: 11rem">FBSITEM ID</th>
              <th class="bg-black-alpha-70 text-white" style="width: 8rem">ROUTE NO</th>
              <th class="bg-black-alpha-70 text-white"  style="width: 8rem">CHQ A/C No</th>
              <!--th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 20rem">CHQ AMOUNT</th-->
              <th class="bg-black-alpha-70 text-white" style="text-align: right; width: 8rem">CHQ AMOUNT</th>
              <th class="bg-black-alpha-70 text-white"  style="width: 5rem">CHQ No.</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-fbsItem>
            <tr [pSelectableRow]="fbsItem">
              <td>{{ fbsItem.itemID }}</td>
              <td>{{ fbsItem.routeNo }}</td>
              <td>{{ fbsItem.chqAcNo }}</td>
              <td style="text-align: right" inputmode="decimal">{{fbsItem.chqAmount | currency}}</td>
              <td>{{ fbsItem.serNo }}</td>
            </tr>
          </ng-template>
        </p-table>
  </div>
  <div class="col-7 card bg-blue-100" style="height:530px; width: 840px; margin-left: 30px">
    <div class="card p-fluid">
      <div class="field grid">
        <!--label class="col-12 mb-2 md:col-1 md:mb-0">{{ fbsItemId }}</label-->
        <div class="col-fixed" style="width:100px">Batch Id:</div>
        <div class="col-3" style="font-weight: bold">{{selectedItem.batchID}}</div>
        <div class="col-fixed" style="width:100px">Branch Code:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.branchCode}}</div>
        <div class="col-fixed" style="width:100px">Batch Date:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.procDate?.substring(0,10)}}</div>
      </div>

      <div class="field grid">
        <!--label class="col-12 mb-2 md:col-1 md:mb-0">{{ fbsItemId }}</label-->
        <div class="col-fixed" style="width:100px">Item Id:</div>
        <div class="col-3" style="font-weight: bold">{{selectedItem.itemID}}</div>
        <div class="col-fixed" style="width:100px">Beneficiary #:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.custAcNo}}</div>
        <div class="col-fixed" style="width:100px">Amount:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.chqAmount | currency}}</div>
      </div>

      <div class="field grid">
        <div class="col-fixed" style="width:100px">Routing No.:</div>
        <div class="col-3" style="font-weight: bold">{{selectedItem.routeNo}}</div>
        <div class="col-fixed" style="width:100px">Cheque No.:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.serNo}}</div>
        <div class="col-fixed" style="width:100px">Chq. Date:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.chqDate?.substring(0,10)}}</div>
      </div>

      <div class="field grid">
        <div class="col-fixed" style="width:100px">Bank No.:</div>
        <div class="col-3" style="font-weight: bold">{{selectedItem.bankNo}}</div>
        <div class="col-fixed" style="width:100px">Chq A/C No.:</div>
        <div class="col-2" style="font-weight: bold">{{selectedItem.chqAcNo}}</div>
      </div>
    </div>

    <!--div class="card bg-black-alpha-10" style="height:250px"-->
      <p-tabView>
        <p-tabPanel header="Chq Fromt Image">
          <p-image [src]='fImage' alt="Front Image" height = "250px" width="650px" [preview] = "true"></p-image>
          <!--img [src]='fImage' alt="Front Image" height = "230px" width="600px"-->
        </p-tabPanel>
        <p-tabPanel header="Chq Rear Image">
          <!--img [src]='rImage' alt="Rear Image" height = "250px" width="600px"-->
          <p-image [src]='rImage' alt="Rear Image" height= 230px width="650px" [preview] = "true" previewImageSizes="40%"></p-image>
        </p-tabPanel>
      </p-tabView>
    <!--/div-->
  </div>
</div>

