import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../interfaces/batch";
import {itemModel} from "../interfaces/items";
import {environment} from "../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class AchBatchService {
  urlStr: string = '';

  constructor(private http: HttpClient) { }

  fetchBatchData(achDate: string) {
    this.urlStr = environment.baseUrl+'nchbatchlistIB/' + achDate
    //console.log(this.urlStr)
    return this.http.get<batchModel[]>(this.urlStr);
  }

  fetchPendingBatchData() {
    this.urlStr = environment.baseUrl+'nchbatchIB/pending'
    return this.http.get<batchModel[]>(this.urlStr);
  }

  fetchBatchItems(batchRecKey: any) {
    this.urlStr = environment.baseUrl+'nchbatch/items/' + batchRecKey
    //console.log("Http Put, itemId:  "+itemId)
    //const body = JSON.stringify({batchid: batchid.trim(), filename: filename});
    //console.log("Body" +body)
    return this.http.get<itemModel[]>(this.urlStr);
  }

  fetchACHItems(achDate: string) {
    this.urlStr = environment.baseUrl+'nchbatch/items/' + achDate
    //console.log("Http Put, itemId:  "+itemId)
    //const body = JSON.stringify({batchid: batchid.trim(), filename: filename});
    //console.log("Body" +body)
    return this.http.get<itemModel[]>(this.urlStr);
  }
}
