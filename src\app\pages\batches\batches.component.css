.p-calendar .p-inputtext {
  font-family: var(--font-family);
  font-feature-settings: var(--font-feature-settings, normal);
  font-size: 1rem !important;
  color: #212529;
  background: #b2e3eb !important;
  padding: 0.5rem 0.75rem;
  border: 2px solid #ced4da;
  transition: background-color 0.15s, border-color 0.15s, box-shadow 0.15s;
  appearance: none;
  border-radius: 4px;
}

.p-inputtext-sm .p-inputtext {
  font-size: 1rem;
  padding: 0.4375rem 0.65625rem;
}

.p-input-filled .p-inputtext {
  background-color: #b2e3eb !important;
}
h4 {
  color: #073d8b;
  margin: 0 0 0 0;
}
.ui-state-highlight {
  background: dodgerblue;
}
