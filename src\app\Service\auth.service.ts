import {inject, Injectable} from '@angular/core';
import {UserService} from "./user.service";

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  isLogged: Boolean = false;
  userService: UserService = inject(UserService);

  login(username: string, password: string){
    //console.log("Username: "+username, password);
    let user = this.userService.users.find((u) => u.username === username
      && u.password === password);
    //console.log(user)

    if(user === undefined)
      this.isLogged = false;
    else
      this.isLogged = true;
    return user;
  }

  logout(){
    this.isLogged = false;
  }

  IsAuthenticated() {
    //console.log("User authenticated: "+this.isLogged);
    return this.isLogged;
  }

}
