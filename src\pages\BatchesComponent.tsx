import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Calendar } from 'primereact/calendar';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { useBatchService } from '../hooks/useBatchService';
import { useToast } from '../hooks/useToast';
import { BatchModel } from '../types/batch';

export const BatchesComponent: React.FC = () => {
  const [batchDate, setBatchDate] = useState<Date>(new Date());
  const [batches, setBatches] = useState<BatchModel[]>([]);
  const [selectedBatch, setSelectedBatch] = useState<BatchModel | null>(null);
  const [previousDate, setPreviousDate] = useState<Date>(new Date());
  
  const navigate = useNavigate();
  const { getBatchData, loading } = useBatchService();
  const { toast, showInfo } = useToast();
  
  const maxDate = new Date();

  useEffect(() => {
    const dateString = batchDate.toISOString().split('T')[0];
    loadBatches(dateString);
  }, []);

  const loadBatches = async (fbsDate: string) => {
    try {
      const data = await getBatchData(fbsDate);
      setBatches(data);
      showInfo(`${data.length} Batches loaded`);
    } catch (error) {
      showInfo('Error', 'Failed to load batch data');
    }
  };

  const onDateSelected = (date: Date | null) => {
    if (!date) return;
    
    setBatchDate(date);
    const dateString = date.toISOString().split('T')[0];
    
    if (date.toDateString() !== previousDate.toDateString()) {
      loadBatches(dateString);
      setPreviousDate(date);
    } else {
      showInfo('Date already selected', date.toDateString());
    }
  };

  const onRowSelect = (event: any) => {
    const batchId = event.data.batchId;
    console.log("RowSelect..." + batchId);
    navigate(`/fbsitems/${batchId}`);
  };

  const onRowUnselect = (event: any) => {
    console.log("RowUnselect..." + event.data.batchId);
  };

  const currencyBodyTemplate = (rowData: BatchModel) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(rowData.depositAmount || 0);
  };

  const dateBodyTemplate = (rowData: BatchModel) => {
    return rowData.fbsDate ? new Date(rowData.fbsDate).toLocaleDateString() : '';
  };

  return (
    <div>
      <h4 className="font-bold flex justify-content-center">BATCH LIST</h4>
      <Toast ref={toast} />
      
      <div className="grid">
        <div className="col-2">
          <Button
            label="Print Batch Details"
            icon="pi pi-chevron-left"
            style={{ marginBottom: '15px', marginLeft: '10px', width: '200px' }}
          />
          <Button
            label="Print Batch List"
            icon="pi pi-chevron-left"
            style={{ marginBottom: '15px', marginLeft: '10px', width: '200px' }}
          />
        </div>
        
        <div className="col-10">
          <div className="flex-auto">
            <label className="font-bold block ml-3 mb-1">Batch Scan Date</label>
          </div>
          
          <Calendar
            value={batchDate}
            onChange={(e) => onDateSelected(e.value)}
            showIcon
            maxDate={maxDate}
            dateFormat="dd M, yy"
            className="p-inputtext-sm font-medium font-bold block justify-content-center ml-3 mb-2"
            style={{ fontSize: 'large' }}
          />
          
          <div className="card bg-black-alpha-20">
            <DataTable
              value={batches}
              selection={selectedBatch}
              onSelectionChange={(e) => setSelectedBatch(e.value)}
              dataKey="batchId"
              selectionMode="single"
              onRowSelect={onRowSelect}
              onRowUnselect={onRowUnselect}
              tableStyle={{ minWidth: '50rem' }}
              scrollable
              scrollHeight="490px"
              className="p-datatable-sm p-datatable-gridlines"
              loading={loading}
            >
              <Column selectionMode="single" headerStyle={{ width: '4rem' }} />
              <Column 
                field="batchId" 
                header="BATCH ID" 
                headerClassName="bg-black-alpha-70 text-white"
              />
              <Column 
                field="itemCount" 
                header="ITEM COUNT"
                headerClassName="bg-black-alpha-70 text-white"
              />
              <Column 
                field="depositAmount" 
                header="DEPOSIT AMOUNT"
                headerClassName="bg-black-alpha-70 text-white"
                body={currencyBodyTemplate}
              />
              <Column 
                field="fbsDate" 
                header="FBS DATE"
                headerClassName="bg-black-alpha-70 text-white"
                body={dateBodyTemplate}
              />
              <Column 
                field="status" 
                header="STATUS"
                headerClassName="bg-black-alpha-70 text-white"
              />
            </DataTable>
          </div>
        </div>
      </div>
    </div>
  );
};
