import {Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ToastModule} from "primeng/toast";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../../interfaces/batch";
import {TableHeaderCheckboxToggleEvent, TableModule} from "primeng/table";
import {CurrencyPipe, DatePipe, formatDate} from "@angular/common";
import {Router} from "@angular/router";
import {ConfirmationService, MessageService} from "primeng/api";
import {AchBatchService} from "../../Service/achbatch.service";
import {DomSanitizer} from "@angular/platform-browser";
import {Ripple, RippleModule} from "primeng/ripple";
import {RespMessage} from "../../interfaces/respmesg";
import {GenericService} from "../../Service/generic.service";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {ConfirmDialogModule} from "primeng/confirmdialog";
import {interval, Subscription} from "rxjs";
import {DialogModule} from "primeng/dialog";

@Component({
  selector: 'app-achinbatch',
  standalone: true,
  imports: [
    ToastModule,
    CalendarModule,
    FormsModule,
    TableModule,
    CurrencyPipe,
    DatePipe,
    ProgressSpinnerModule,
    ConfirmDialogModule,
    DialogModule,
    RippleModule
  ],
  templateUrl: './achinbatch.component.html',
  styleUrl: './achinbatch.component.css',
  providers: [ConfirmationService, MessageService]
})
export class AchInbatchComponent implements OnInit, OnDestroy {
  private http = inject(HttpClient);
  batchDate: Date = new Date;
  maxDate: Date = new Date;
  previousDate: Date = new Date;
  selectedDate: Date = new Date;
  batches!: batchModel[];
  batch!: batchModel;
  selectedBatch!: batchModel;
  highlighted: any;
  dateString: string = '';
  noOfBatches: number = 0;
  origBank: string = '';
  isChecked: any [];
  batchRecId: any;
  httpRslt: any
  respMsg!: RespMessage;
  showSpinnerDialog: boolean = false;
  itemsChecked: number = 0;
  strRecIdNos: string[] | undefined;
  nbrRecIdNos: number[] | undefined;
  updateSubscription: Subscription | undefined;
  minute = 60000;

  constructor(private achBatchService: AchBatchService,
              private router: Router,
              private genericService: GenericService,
              private messageService: MessageService,
              private confirmationService: ConfirmationService) {
    this.isChecked = [];
  }

  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.showBatches(this.dateString);
    // *** Code to refresh EPayFiles Table view every minute ***
    // this.updateSubscription = interval(3*this.minute).subscribe(
    //   () => {
    //     this.showBatches(this.dateString);
    //   });
  }

  refreshPage() {
    this.showBatches(this.dateString);
  }

  showBatches(achDate: string) {
    this.achBatchService.fetchBatchData(achDate).subscribe(item => {
      this.batches = [];
      this.noOfBatches = item.length;
      item.forEach(item => {
        this.batch = item;
        switch (this.batch.odfiNbr?.substring(5, 8)) {
          case "002":
            this.origBank = "Scotia Bank"
            break
          case "003" || "044":
            this.origBank = "Royal Bank"
            break
          case "007":
            this.origBank = "Fidelity Bank"
            break
          case "008":
            this.origBank = "R F Bank & Trust"
            break
          case "009":
            this.origBank = "Teachers & Salaried WCCU"
            break
          case "010":
            this.origBank = "First Caribbean"
            break
          case "021":
            this.origBank = "Commonwealth Bank"
            break
          case "100":
            this.origBank = "Central Bank"
            break
          case "265":
            this.origBank = "Citi Bank"
            break
        }
        this.batch.odfiName = this.origBank
        this.batch.isSelected = false;
        switch (this.batch.recInd) {
          case 0:
            this.batch.recStatus = "PENDING"
            break
          case 1:
            this.batch.recStatus = "PROCESSED"
            //this.batch.cbEnabled = true
            break
          case 2:
            this.batch.recStatus = "CHARGE BACK"
            break;
          case 3:
            this.batch.recStatus = "PROCESSED"
            break;
        }

        this.batches.push(this.batch);
      })
    })
  }

  onDateSelected(event: Date) {
    this.selectedDate = event;
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    if (this.batchDate.toDateString() != this.previousDate.toDateString()) {
      this.showBatches(this.dateString);
      this.previousDate = this.batchDate;
    }
  }

  ngOnDestroy(): void {
    //this.updateSubscription?.unsubscribe()
  }

  onRowSelect(batchItem: any) {
    //console.log("Row selected successfully... [" + batchItem.rowIndex + "]");
  }

  onRowUnselect($event: any) {

  }

  selectedRow(batchItem: any): void {
    //console.log("Row selected successfully... [" + batchItem.rowIndex + "]");
    this.highlighted = batchItem
    this.batchRecId = batchItem.recId
    //console.log("Batch RecId..."+this.batchRecId)
    this.router.navigateByUrl('achitems/' + this.batchRecId);
  }


  headerCheckboxToggle($event: TableHeaderCheckboxToggleEvent) {
    //console.log("Header Checkbox Event :: " + $event.checked);
    if (this.batches.length > 0) {
      for (let i = 0; i < this.batches.length; i++) {
        this.batches[i].isSelected = $event.checked;
      }
    }
  }

  cbOnClick(batchItem: any) {
    // **** When the Checkbox is clicked the result shown is the state of the CB at the time it is clicked.
    // **** Unchecked = False, Checked = True
    //console.log("CB Click event :: Item Selected: "+batchItem.recId+", "+batchItem.batchId+", "+batchItem.isSelected);
  }

  OnChangeEvent(batchItem: any) {
    //console.log("On ChangeEvent", batchItem);
  }

  generateHostFile() {
    this.showSpinnerDialog = true
    this.httpRslt = this.genericService.generateHostACHFile(this.dateString).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog = false
      this.messageService.add({
        severity: 'info',
        summary: this.respMsg.message
      });
      this.showBatches(this.dateString);
    })
  }

  generateCruiseFile() {
    this.nbrRecIdNos = [];
    for (let i = 0; i < this.batches.length; i++) {
      if (this.batches[i].recInd == 0) {
        this.nbrRecIdNos.push(Number(this.batches[i].recId))
        //console.log("Row Index = " + i + ", " + this.batches[i].recId +", " + this.batches[i].batchId + ", " + this.batches[i].recInd);
      }
    }
    if (this.nbrRecIdNos.length > 0) {
      this.showSpinnerDialog = true
      //console.log(this.nbrRecIdNos.toString())
      this.httpRslt = this.genericService.generateCruiseFile(this.nbrRecIdNos.toString(), this.dateString).subscribe(resp => {
        this.respMsg = resp
        this.showSpinnerDialog = false
        this.messageService.add({
          severity: 'info',
          summary: this.respMsg.message
        });
        this.showBatches(this.dateString);
      })
    }
    else {
      this.messageService.add({
        severity: 'info',
        summary: "No Batch was selected for printing.."
      });
    }
  }


  printBatchReport() {
    this.showSpinnerDialog = true
    this.httpRslt = this.genericService.generateReportACHRep01(this.dateString).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog = false
      //console.log("Message:    "+this.respMsg.message)
      //console.log("HttpStatus: "+this.respMsg.status)
      this.messageService.add({
        severity: 'info',
        summary: this.respMsg.message
      });
    })
  }

  printCheckedBatches() {
    //console.log("Number of batches " + this.batches.length);
    this.nbrRecIdNos = [];
    for (let i = 0; i < this.batches.length; i++) {
      if (this.batches[i].isSelected) {
        this.itemsChecked++;
        this.nbrRecIdNos.push(Number(this.batches[i].recId))
        //console.log("Row Index = " + i + ", " + this.batches[i].recId +", " + this.batches[i].batchId + ", IsCheck? " + this.batches[i].isSelected);
        this.batches[i].isSelected = false;
        //console.log("Row Index = " + i + ", " + this.batches[i].recId +", " + this.batches[i].batchId + ", IsCheck? " + this.batches[i].isSelected);
      }
    }
    //console.log(this.nbrRecIdNos)
    if (this.nbrRecIdNos.length > 0) {
      this.showSpinnerDialog = true
      this.httpRslt = this.genericService.generateReportACHRep03(this.dateString, this.nbrRecIdNos.toString()).subscribe(resp => {
        this.respMsg = resp
        this.showSpinnerDialog = false
        //console.log("Message:    "+this.respMsg.message)
        //console.log("HttpStatus: "+this.respMsg.status)
        this.messageService.add({
          severity: 'info',
          summary: this.respMsg.message
        });
      })
    }
    else {
      this.messageService.add({
        severity: 'info',
        summary: "No Batch was selected for printing.."
      });
    }
  }

  confirm(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to proceed?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      accept: () => {
        this.generateCruiseFile()
        //this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted' });
      },
    });
  }

  pendingOnlyBatches() {
    this.achBatchService.fetchPendingBatchData().subscribe(item => {
      this.batches = [];
      this.noOfBatches = item.length;
      //console.log("Pending Batches: "+this.noOfBatches)
      item.forEach(item => {
        this.batch = item;
        //console.log(this.batch.odfiNbr)
        switch (this.batch.odfiNbr?.substring(5, 8)) {
          case "001":
            this.origBank = "Bank of Bahamas"
            break
          case "002":
            this.origBank = "Scotia Bank"
            break
          case "003" || "044":
            this.origBank = "Royal Bank"
            break
          case "007":
            this.origBank = "Fidelity Bank"
            break
          case "008":
            this.origBank = "R F Bank & Trust"
            break
          case "010":
            this.origBank = "First Caribbean"
            break
          case "021":
            this.origBank = "Commonwealth Bank"
            break
          case "100":
            this.origBank = "Central Bank"
            break
          case "265":
            this.origBank = "Citi Bank"
            break
        }

        this.batch.odfiName = this.origBank
        this.batch.isSelected = false;
        this.batch.recStatus = "PENDING";
        //console.log(this.batch);
        this.batches.push(this.batch);
      })
    })
  }
}
