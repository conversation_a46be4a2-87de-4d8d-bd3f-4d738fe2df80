import {ApplicationConfig, importProvidersFrom, provideZoneChangeDetection} from '@angular/core';
import {provideRouter, RouteReuseStrategy} from '@angular/router';
import { routes } from './app.routes';
import { BrowserModule } from "@angular/platform-browser";
import { provideHttpClient } from "@angular/common/http";
import {MessageService} from "primeng/api";
import { provideAnimationsAsync} from "@angular/platform-browser/animations/async";
import {HashLocationStrategy, LocationStrategy} from "@angular/common";
import {RouteReuseService} from "./Service/RouteReuse.service";
import {BnNgIdleService} from "bn-ng-idle";


export const appConfig: ApplicationConfig = {
  providers: [
    MessageService,
    BnNgIdleService,
    provideHttpClient(),
    importProvidersFrom(BrowserModule),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideAnimationsAsync(),
    { provide: LocationStrategy, useClass: HashLocationStrategy },
    { provide: RouteReuseStrategy, useClass: RouteReuseService }
  ]
};
