import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../API/batch";

@Injectable({
  providedIn: 'root'
})
export class BatchService {

  constructor(private http: HttpClient) { }

  getBatchData(fbsDate: string) {
    return this.http.get<batchModel[]>('http://localhost:8080/api/batchList/'+fbsDate);
  }

  getBatches() {
    return this.http.get<batchModel[]>('http://localhost:8080/api/batchList')
    //  .toPromise()
    //  .then(res => res.data as Batch[])
    //  .then(data => data);
  }

  fetchBatches() {
    return this.http.get('http://localhost:8080/api/batchList')
      .subscribe((res:any)=> {
      console.log(res.data);
    });
  }
}
