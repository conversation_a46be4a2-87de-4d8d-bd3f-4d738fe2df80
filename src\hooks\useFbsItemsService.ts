import { useState } from 'react';
import { apiClient } from '../services/apiService';
import { FbsItemModel, FbsImageModel } from '../types/fbsItem';

export const useFbsItemsService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getFBSItemData = async (batchId: string): Promise<FbsItemModel[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<FbsItemModel[]>(`fbsitems/${batchId}`);
      return response.data;
    } catch (err) {
      setError('Failed to fetch FBS item data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getFBSImageData = async (itemId: string): Promise<FbsImageModel> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<FbsImageModel>(`fbsImage/${itemId}`);
      return response.data;
    } catch (err) {
      setError('Failed to fetch FBS image data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getFBSDupData = async (dupChqParams: string): Promise<FbsItemModel[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<FbsItemModel[]>(`dupitems/${dupChqParams}`);
      return response.data;
    } catch (err) {
      setError('Failed to fetch duplicate data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getFbsChqData = async (iflChqData: string): Promise<FbsItemModel[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<FbsItemModel[]>(`fbsmatchItem/${iflChqData}`);
      return response.data;
    } catch (err) {
      setError('Failed to fetch FBS check data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getFbsItem = async (itemId: string): Promise<FbsItemModel[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get<FbsItemModel[]>(`fbsMatchedItem/${itemId}`);
      return response.data;
    } catch (err) {
      setError('Failed to fetch FBS item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    getFBSItemData,
    getFBSImageData,
    getFBSDupData,
    getFbsChqData,
    getFbsItem,
    loading,
    error
  };
};
