import { Component } from '@angular/core';
import {LayoutService} from "../layout.service";
import {ButtonModule} from "primeng/button";
import {RippleModule} from "primeng/ripple";

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [ButtonModule, RippleModule],
  templateUrl: './footer.component.html',
})
export class FooterComponent {
  constructor(public layoutService: LayoutService) {}
}
