import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../interfaces/batch";
import {itemModel} from "../interfaces/items";
import {RespMessage} from "../interfaces/respmesg";
import {environment} from "../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class AchItemService {
  urlStr: string = '';

  constructor(private http: HttpClient) { }


  fetchNCHItemData(achDate: string) {
    this.urlStr = environment.baseUrl+'nchitemlist/' + achDate
    //console.log(this.urlStr)
    return this.http.get<itemModel[]>(this.urlStr);
  }

  updateRetItemDet(itemRecId: number, retRCode: any, retInd: number) {
    const body = JSON.stringify({itemRecId:itemRecId, retRCode:retRCode, retInd:retInd});
    return this.http.put<any>(environment.baseUrl+'returnItems/updateItem', body)
  }
}
