import {ActivatedRouteSnapshot, DetachedRoute<PERSON><PERSON><PERSON>, RouteReuseStrategy} from '@angular/router';
import {routes} from "../app.routes";

export class RouteReuseService implements RouteReuseStrategy {
  private handlers: { [key: string]: DetachedRouteHandle } = {};
  result: boolean = false

  shouldDetach(route: ActivatedRouteSnapshot): boolean {
    //console.log('Executing::shouldDetach')

    let shouldReuse = true;

    if (!route.routeConfig || route.routeConfig.loadChildren) {
      return false;
    }

    let routePath = route.routeConfig.path;
    //console.log('route path: '+routePath)

    if (routePath == 'fbsitems/:batchId' ||
      routePath == 'achitems/:batchRecKey' ||
      routePath == 'login' ||
      routePath == 'dashboard' )
    {
      shouldReuse = false
    }
    return shouldReuse;
  }

  store(route: ActivatedRouteSnapshot, handler: DetachedRouteHandle): void {
    //console.log('Executing::store')
    //console.log("storing handler: "+handler);
    if (handler) {
      this.handlers[this.getUrl(route)] = handler;
    }
    //this.storedRoutes.set(route.routeConfig.path, handler);
  }

  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    let retVal: boolean
    //console.log('Executing::shouldAttach')
    retVal = !!this.handlers[this.getUrl(route)]
    //console.log('Return: '+retVal)
    return retVal;
  }

  retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle {
    //console.log('Executing::retrieve');
    if (!route.routeConfig || route.routeConfig.loadChildren) {
      return false;
    }
    return this.handlers[this.getUrl(route)];
  }
  shouldReuseRoute(future: ActivatedRouteSnapshot, current: ActivatedRouteSnapshot): boolean {
    //console.log('Executing::shouldReuseRoute')
    //debugger
    let reUseUrl = false;
    if (future.routeConfig) {
      if (future.routeConfig.data) {
        reUseUrl = future.routeConfig.data['data'];
      }
    }

    const defaultReuse = (future.routeConfig === current.routeConfig);
    return reUseUrl || defaultReuse;
    //return future.routeConfig === current.routeConfig;

  }

  getUrl(route: ActivatedRouteSnapshot): any{
    if (route.routeConfig) {
      const url = route.routeConfig.path;
      //console.log("getting url: ", url);
      return url;
    }
  }
}
