import { Component } from '@angular/core';
import {DialogModule} from "primeng/dialog";
import {ToastModule} from "primeng/toast";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {RippleModule} from "primeng/ripple";
import {CurrencyPipe} from "@angular/common";
import {TableHeaderCheckboxToggleEvent, TableModule, TableRowSelectEvent, TableRowUnSelectEvent} from "primeng/table";
import {X9ChqModel} from "../../API/x9cheques";

@Component({
  selector: 'app-x9inclr',
  standalone: true,
  imports: [
    DialogModule,
    ToastModule,
    CalendarModule,
    FormsModule,
    RippleModule,
    CurrencyPipe,
    TableModule
  ],
  templateUrl: './x9inclr.component.html',
  styleUrl: './x9inclr.component.css'
})
export class X9InclrComponent {
  procDate: Date = new Date();
  maxDate: Date = new Date();
  x9IBChqs!: X9ChqModel[];
  selectedX9Chq!: X9ChqModel;
  highlighted: any;

  onDateSelected($event: Date) {

  }

  refreshList() {

  }

  generateX9File() {

  }

  printX9OutClrReport1() {

  }

  onRowSelect($event: TableRowSelectEvent) {
    
  }

  onRowUnselect($event: TableRowUnSelectEvent) {
    
  }

  headerCheckboxToggle($event: TableHeaderCheckboxToggleEvent) {
    
  }

  cbOnClick(chequeItem: any) {
    
  }

  OnChangeEvent(chequeItem: any) {
    
  }

  selectedRow(chequeItem: any) {
    
  }
}
