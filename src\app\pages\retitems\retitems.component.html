<h4 class = "font-bold flex justify-content-center" >WORK WITH OUTGOING RETURN ITEMS</h4>
<p-toast />
<p-confirmDialog />
<div class="grid">

  <div class="flex-auto">
    <div class="font-bold block ml-3 mb-1" style="font-size: 15px">ACH Date</div>
    <div class="inline-flex bottom-0">
      <p-calendar [(ngModel)]="achDate"
                  [showIcon]="true"
                  [maxDate]="maxDate"
                  dateFormat="dd M,  yy"
                  inputId="calendarBtn"
                  class="p-inputtext-sm block ml-3 mb-2"
                  [style]="{'font-size': 'large'}"
                  (onSelect)="onSelectedDate($event)">
      </p-calendar>
      <button class ="inline" pButton pRipple label="Generate Return File" icon="pi pi-save" (click)="showDateDialog()"
              style="margin-bottom: 15px; margin-left:30px"></button>

      <button class ="inline" pButton pRipple label="Print Items Selected to Return" icon="pi pi-print" (click)="printReportRetRep01()"
              style="margin-bottom: 15px; margin-left:30px"></button>

      <button class ="inline" pButton pRipple label="Print Returned Items Report" icon="pi pi-print" (click)="printReportRetRep02()"
              style="margin-bottom: 15px; margin-left:30px"></button>

    </div>
  </div>
  <div class="col-12 card bg-blue-100" style="height: 545px; width: 98%; margin-left: 10px">
    <p-table [value]="rtnItems"
             dataKey="recID"
             selectionMode="single"
             [(selection)]="selectedItem"
             [tableStyle]="{'min-width': '30rem'}"
             [scrollable]="true"
             (onRowSelect)="onRowSelect($event)"
             [virtualScroll]="false"
             [virtualScrollItemSize]="25"
             scrollHeight="525px"
             styleClass="p-datable-sm p-datatable-gridlines">
      <ng-template pTemplate="header">
        <tr>
          <th class="bg-black-alpha-70 text-white" style="width: 2rem">ORIG FILENAME</th>
          <th class="bg-black-alpha-70 text-white" style="width: 2.5rem">BATCH ID</th>
          <th class="bg-black-alpha-70 text-white" style="width: 3rem">ROUTE NO</th>
          <th class="bg-black-alpha-70 text-white"  style="width: 6rem">A/C NUMBER</th>
          <th class="bg-black-alpha-70 text-white" style="width: 6rem">A/C TYPE</th>
          <th class="bg-black-alpha-70 text-white" style="width: 8rem">INDV. ID</th>
          <th class="bg-black-alpha-70 text-white" style="width: 8rem">INDV. NAME</th>
          <th class="bg-black-alpha-70 text-white" style="text-align: right; width: 6rem">CHQ AMOUNT</th>
          <th class="bg-black-alpha-70 text-white" style="width: 15px"></th>
          <th class="bg-black-alpha-70 text-white"  style="width: 8rem">RETURN REASON</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rtnItem>
        <tr [pSelectableRow]="rtnItem" [ngStyle]="{height: '20px'}">
          <td style="height: 25px">{{rtnItem.nchFileName}}</td>
          <td style="height: 25px">{{rtnItem.batchId}}</td>
          <td style="height: 25px">{{rtnItem.routeNo}}</td>
          <td style="height: 25px">{{rtnItem.acctNo}}</td>
          <td style="height: 25px">{{rtnItem.acctType}}</td>
          <td style="height: 25px">{{rtnItem.indvId}}</td>
          <td style="height: 25px">{{rtnItem.indvName}}</td>
          <td style="height: 25px; text-align: right" inputmode="decimal">{{rtnItem.entryAmt | currency}}</td>
          <td style="height: 25px; text-align: center">
            <p-checkbox
              [binary]="true"
              [disabled]="checkboxDisabled"
              [(ngModel)]="rtnItem.checkBox"
              [value]="rtnItem"
              (click)="chkBoxClick(rtnItem)"
              (onChange)="chkBoxChange($event)"
              [style]="{width: '18px', height: '15px', bm: '15px'}">
            </p-checkbox>
          </td>
          <td>{{rtnItem.retRCode}}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog header="Processing, pleas wait.."
          [(visible)]="showSpinnerDialog"
          [modal]="true"
          [style]="{ width: '20rem' }"
          position="center">
  <div class="card flex justify-content-center">
    <p-progressSpinner
      styleClass="w-4rem h-4rem"
      strokeWidth="8"
      fill="var(--surface-ground)"
      animationDuration=".5s" />
  </div>
</p-dialog>

<p-dialog header="Select Return Reason"
          [(visible)]="displayReturnCodes"
          [modal]="true"
          [style]="{ width: '31rem' }"
          (onShow)="diagShow()"
          (onHide)="diagHide(selectedItem)"
          position="center">
  <p-dropdown appendTo="body"
              [options]="retCodes"
              [(ngModel)]="rtnItemCode"
              [style]="{ width: '28rem' }"
              (onChange)="drpDwnChange(selRetItem)"
              (onHide)="drpDwnHide(selRetItem)"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Return Reason">
  </p-dropdown>
</p-dialog>
<p-dialog header="Select ACH Return Data"
          [(visible)]="displayDateDialog"
          [modal]="true"
          [style]="{ width: '27rem', height: '33rem' }"
          (onShow)="dateDialogShow()"
          (onHide)="dateDialogHide(selectedItem)"
          position="center">
  <p-calendar [(ngModel)]="achRetDate"
              dateFormat="dd M,  yy"
              (onSelect)="onSelectedRetDate($event)">
  </p-calendar>
</p-dialog>



