import { Routes } from '@angular/router';
import {DesktopComponent} from "./layout/desktop.component";
import {LoginComponent} from "./pages/login/login.component";
import {DashboardComponent} from "./layout/dashboard/dashboard.component";
import {BatchesComponent} from "./pages/batches/batches.component";
import {DupitemsComponent} from "./pages/dupitems/dupitems.component";
import {FbsitemsComponent} from "./pages/fbsitems/fbsitems.component";
import {UnmatchitemsComponent} from "./pages/unmatchitems/unmatchitems.component";
import {X9OutClrComponent} from "./pages/x9outclr/x9outclr.component";
import {CanActivate} from "./guard/auth.guard";
import {AchInbatchComponent} from "./pages/achinbatch/achinbatch.component";
import {AchOBFilesComponent} from "./pages/achoutbatch/achobfiles.component";
import {AchitemsComponent} from "./pages/achitems/achitems.component";
import {RetitemsComponent} from "./pages/retitems/retitems.component";
import {X9InclrComponent} from "./pages/x9inclr/x9inclr.component";

export const routes: Routes = [
  {path: '', redirectTo: 'login', pathMatch:'full'},
  {path: 'login', component: LoginComponent},
  {
    path: '',
    component: DesktopComponent,
    children: [
      {path: 'dashboard', component: DashboardComponent, canActivate: [CanActivate]},
      {path: 'batches', 'title': 'Batches', component: BatchesComponent, canActivate: [CanActivate]},
      {path: 'fbsitems', 'title': 'FBS Items', component: FbsitemsComponent, canActivate: [CanActivate]},
      {path: 'fbsitems/:batchId', 'title': 'FBS Items', component: FbsitemsComponent, canActivate: [CanActivate]},
      {path: 'dupitems', 'title': 'Duplicate Items', component: DupitemsComponent, canActivate: [CanActivate]},
      {path: 'unmatchitems', 'title': 'Unmatched Items', component: UnmatchitemsComponent, canActivate: [CanActivate]},
      {path: 'x9inclr', 'title': 'X9 InClearing', component: X9InclrComponent, canActivate: [CanActivate]},
      {path: 'x9outclr', 'title': 'X9 Out Clearing', component: X9OutClrComponent, canActivate: [CanActivate]},
      {path: 'achinbatch', 'title': 'Incoming Files', component: AchInbatchComponent, canActivate: [CanActivate]},
      {path: 'achobfiles', 'title': 'Outgoing Files', component: AchOBFilesComponent, canActivate: [CanActivate]},
      {path: 'achitems/:batchRecKey', 'title': 'NACHA Items',component:AchitemsComponent, canActivate: [CanActivate]},
      {path:'rtnitems', 'title': 'Return Items',component:RetitemsComponent, canActivate: [CanActivate]},
    ]
  },
];
