import { Injectable } from '@angular/core';
import {User} from "../Models/user";

@Injectable({
  providedIn: 'root'
})

@Injectable({
  providedIn: 'root'
})

export class UserService {
  users: User[] = [
    new User(1, 'Administrator', 'admin', 'password', 'Admin'),
    new User(2, 'SuperUser', 'supv', 'password', 'Super'),
    new User(3, 'Mark Vought', 'mv', '12345', 'User'),
    new User(4, 'Sarah King', 'sk', '12345', 'User')
  ]
}
