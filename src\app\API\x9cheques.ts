export interface X9ChqModel {
  iflRefKey?: string;
  iflChqRouteNo?: string;
  iflChqSerNo?: string;
  iflChqAcctNo?: string;
  iflBenAcctNo?: string;
  iflChqAmount?: number;
  iflFBSItemId?: string;
  fbsItemId?: string;
  fbsChqRouteNo?: string;
  fbsChqAcctNo?: string;
  fbsChqSerNo?: string;
  fbsBenAcctNo?: string;
  fbsChqAmount?: number;
  fbsChqDate?: string;
  fbsIFLRefKey?: string;
  fbsBatchId?: string;
  fbsBatchDate?: string;
  fbsX937Ind?: number;
  isSelected?: boolean;
  chqStatus?: string;
  x9Filename?:string;
  fbsProcDate?:string;
}
