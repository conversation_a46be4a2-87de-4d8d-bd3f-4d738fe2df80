import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {iflUnmatchModel, iflTransModel} from "../API/ifltransmodel";

@Injectable({
  providedIn: 'root'
})
export class IfltransService {

  constructor(private http: HttpClient) { }

  getIflTransData(fbsDate: string) {
    return this.http.get<iflUnmatchModel[]>('http://localhost:8080/api/iflUnmatchItems/'+fbsDate);
  }

  updateIflTrans(iflRefNo: string, fbsItemId: string) {
    //const body = JSON.stringify("iflRefNo:" + iflRefNo+", fbsItem:" + fbsItem);
    const body = JSON.stringify({iflRefKey:iflRefNo, fbsItemId:fbsItemId});
    console.log("Body" +body)
    return this.http.put<any>('http://localhost:8080/api/iflTrans/updateIFLTrans', body);
  }

}
