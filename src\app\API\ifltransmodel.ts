export interface iflUnmatchModel {
  iflRefNo?: string;
  itemId?: string;
  routeNo?: string;
  chqSerNo?: string;
  chqAmount?: number;
  chqDate?: Date;
  chqAcct?: string;
  benAcct?: string;
  tranDate?: Date;
  tranTime?: Date;
  trnBranch?: string;
}

export interface iflTransModel {
  tranRefno?: string;
  routingNumber?: string;
  serialNo?: string;
  chqAmount?: number;
  chqDate?: Date;
  chqAccount?: string;
  beneficiaryAccount?: string;
  tranDate?: Date;
  tranTime?: Date;
  tranBranch?: string;
  tranTellerID?: string;
  fbMatchInd?: number;
  fbsItemId?: string;
  x9OutInd?: number;
  x9OutDate?: Date;
  fbMatchDate?: Date;
  exceptionMessage?: string;
}
