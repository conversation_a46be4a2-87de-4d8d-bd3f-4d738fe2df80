import {Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ToastModule} from "primeng/toast";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../../interfaces/batch";
import {TableHeaderCheckboxToggleEvent, TableModule} from "primeng/table";
import {CurrencyPipe, DatePipe, formatDate} from "@angular/common";
import {Router} from "@angular/router";
import {MessageService} from "primeng/api";
import {AchBatchService} from "../../Service/achbatch.service";
import {DomSanitizer} from "@angular/platform-browser";
import {Ripple} from "primeng/ripple";
import {RespMessage} from "../../interfaces/respmesg";
import {GenericService} from "../../Service/generic.service";
import {DialogModule} from "primeng/dialog";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {FilesModel} from "../../interfaces/files";
import {AchFilesService} from "../../Service/achfiles.service";
import {interval, Subscription} from "rxjs";

@Component({
  selector: 'app-achobfiles',
  standalone: true,
  imports: [
    ToastModule,
    CalendarModule,
    FormsModule,
    TableModule,
    CurrencyPipe,
    DatePipe,
    DialogModule,
    ProgressSpinnerModule
  ],
  templateUrl: './achobfiles.component.html',
  styleUrl: './achobfiles.component.css'
})
export class AchOBFilesComponent implements OnInit, OnDestroy{
  private http = inject(HttpClient);
  achDate: Date = new Date;
  maxDate: Date = new Date;
  previousDate: Date = new Date;
  selectedDate: Date = new Date;
  fileList!: FilesModel [];
  file!: FilesModel;
  selectedFile!: FilesModel;
  highlighted: any;
  dateString: string = '';
  noOfFiles: number = 0;
  origBank: string = '';
  isChecked: any [];
  fileRecId: any;
  httpRslt: any
  minute: number = 60000;
  respMsg!: RespMessage;
  showSpinnerDialog: boolean = false;
  nbrRecIdNos: number[] | undefined;
  updateSubscription: Subscription | undefined;
  itemChecked: boolean = false;

  constructor(private achBatchService: AchBatchService,
              private router: Router,
              private achFilesService: AchFilesService,
              private genericService: GenericService,
              private messageService: MessageService) {
    this.isChecked = [];
  }

  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.showACHFiles(this.dateString);
    // *** Code to refresh EPayFiles Table view every minute ***
    // this.updateSubscription = interval(3*this.minute).subscribe(
    //   () => {
    //     this.showACHFiles(this.dateString);
    //   });
  }

  ngOnDestroy(): void {
    //this.updateSubscription?.unsubscribe()
  }

  refreshPage() {
    this.showACHFiles(this.dateString);
  }

  showACHFiles(achDate: string) {
    this.achFilesService.fetchOBACHFiles(achDate).subscribe(items => {
      this.fileList = items;
      this.noOfFiles = items.length;
    })
  }

  onDateSelected(event: Date) {
    this.selectedDate = event;
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    if (this.achDate.toDateString() != this.previousDate.toDateString()) {
      this.showACHFiles(this.dateString);
      this.previousDate = this.achDate;
    }
  }

  onRowSelect(fileItem: any) {
    //console.log("Row selected successfully... [" + fileItem.rowIndex + "]");
  }

  onRowUnselect($event: any) {

  }

  selectedRow(fileItem: any): void {
    //console.log("Row selected successfully... [" + fileItem.fileName+ "]");
    this.highlighted = fileItem
    this.fileRecId = fileItem.recId
    //this.router.navigateByUrl('achitems/'+this.fileRecId);
  }

  cbOnClick(fileItem: any) {
    //this.selectedBatch = batchItem;
    //console.log("CB Click event... [" + fileItem.fileName+ "]");
  }

  headerCheckboxToggle($event: TableHeaderCheckboxToggleEvent) {
    //console.log("Header Checkbox Event :: "+$event.checked);
    if(this.isChecked.length > 0) {
      for (let i = 0; i < this.isChecked.length; i++) {
        this.isChecked[i].isChecked = $event.checked;
      }
    }
  }

  OnChangeEvent(fileItem: any) {
    //console.log("CB Change event... " + fileItem.fileName + " RecId: "+fileItem.recId + " isChecked? "+ fileItem.isSelected);
  }

  printFileListReport() {
    this.showSpinnerDialog = true
    this.httpRslt = this.genericService.generateReportACHRep02(this.dateString, "OBFWD").subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog = false
      this.messageService.add({
        severity: 'info',
        summary: this.respMsg.message
      });
    })
  }

  printFileTrans() {
    this.nbrRecIdNos = [];
    for (let i = 0; i < this.fileList.length; i++) {
      if (this.fileList[i].isSelected) {
        this.nbrRecIdNos.push(Number(this.fileList[i].recId))
        this.fileList[i].isSelected = false;
        //console.log("Row Index = " + i + ", " + this.batches[i].recId +", " + this.batches[i].batchId + ", IsCheck? " + this.batches[i].isSelected);
      }
    }
    if (this.nbrRecIdNos.length > 0) {
      this.showSpinnerDialog = true
      this.httpRslt = this.genericService.generateReportACHRep04(this.nbrRecIdNos.toString(), "OBFWD", this.dateString).subscribe(resp => {
        this.respMsg = resp
        this.showSpinnerDialog = false
        this.messageService.add({
          severity: 'info',
          summary: this.respMsg.message
        });
      })
    }
    else {
      this.messageService.add({
        severity: 'info',
        summary: "No Batch was selected for printing.."
      });
    }
  }
}
