import {Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ActivatedRoute, Router, RouterLink} from "@angular/router";
import {FormsModule} from "@angular/forms";
import {MessageService} from "primeng/api";
import {FbsitemsService} from "../../Service/fbsitems.service";
import {HttpClient} from "@angular/common/http";
import {fbsItemModel} from "../../API/fbsItem";
import {CardModule} from "primeng/card";
import {PanelModule} from "primeng/panel";
import {CarouselModule} from "primeng/carousel";
import {ButtonModule} from "primeng/button";
import {TableModule, TableRowSelectEvent, TableRowUnSelectEvent} from "primeng/table";
import {InputTextModule} from "primeng/inputtext";
import {TabViewModule} from "primeng/tabview";
import {ImageModule} from "primeng/image";
import {DomSanitizer} from '@angular/platform-browser';
import {NgOptimizedImage} from "@angular/common";
import {fbsImageModel} from "../../API/fbsImage";


@Component({
  selector: 'app-fbsitems',
  standalone: true,
  imports: [
    RouterLink,
    FormsModule,
    CardModule,
    PanelModule,
    CarouselModule,
    ButtonModule,
    TableModule,
    InputTextModule,
    TabViewModule,
    ImageModule,
    NgOptimizedImage
  ],
  templateUrl: './fbsitems.component.html',
  styleUrl: './fbsitems.component.css'
})


export class FbsitemsComponent implements OnInit, OnDestroy {
  private http = inject(HttpClient);
  batchNo: string = '';
  fbsItems!: fbsItemModel[];
  selectedItem: fbsItemModel = {
    selected: false
  };
  fbsImage: fbsImageModel = {}
  batchId: any;
  itemId: any;
  fImage?: any;
  rImage?: any;

  constructor(private route: ActivatedRoute,
              private fbsitemsService: FbsitemsService,
              private router: Router,
              private sanitizer: DomSanitizer,
              private messageService: MessageService) {
  }

  ngOnInit(): void {
    this.batchNo = this.route.snapshot.params['batchId']
    this.loadFBSItems(this.batchNo);
    //console.log('FBSItems Component :: OnInit')
    //console.log('Batch Number: '+this.batchNo)
  }

  ngOnDestroy(): void {
    //console.log('FBSItems Component :: OnDestroy')
  }

  loadFBSItems(batchId: string) {
    this.fbsitemsService.getFBSItemData(batchId).subscribe(item => {
      this.fbsItems = item;
      if (this.fbsItems.length > 0) {
        this.selectedItem = this.fbsItems[0];
        //this.fbsitemsService.getFBSImageData(this.selectedItem.itemID)
        this.itemId = this.selectedItem.itemID;

        this.fbsitemsService.getFBSImageData(this.itemId).subscribe(imageData => {
          //console.log('LoadFBSItems :: fbsitemsService.getFBSImageData('+this.itemId.trim()+')')
          this.fbsImage = imageData

          this.fImage = this.fbsImage.frontImageData
          let imgURL = 'data:image/png;base64,'+imageData.frontImageData;
          this.fImage = this.sanitizer.bypassSecurityTrustUrl(imgURL);

          this.rImage = this.fbsImage.rearImageData
          let rImgURL = 'data:image/png;base64,'+imageData.rearImageData;
          this.rImage = this.sanitizer.bypassSecurityTrustUrl(rImgURL);

          //console.log(this.fImage)
          //console.log(this.rImage)
        });
        //this.fImage = "F"+this.selectedItem.itemID?.trim()+".png"
        //this.rImage = "R"+this.selectedItem.itemID?.trim()+".png"
        //this.populateFbsItemObj(this.selectedItem)
        //this.fbsItemObj.batchId = this.selectedItem.batchID;
        //this.fbsItemObj.itemId = this.selectedItem.itemID;
        //console.log(this.fbsItems);
      }
    })
  }

  onRowSelect(event: TableRowSelectEvent) {
    this.selectedItem = event.data
    //console.log("OnRowSelect: "+this.selectedItem.itemID)
    this.itemId = this.selectedItem.itemID;
    //console.log(this.itemId)
    this.fbsitemsService.getFBSImageData(this.itemId).subscribe(item => {
      this.fbsImage = item
      this.fImage = this.fbsImage.frontImageData
      let fImgURL = 'data:image/png;base64,'+item.frontImageData;
      this.fImage = this.sanitizer.bypassSecurityTrustUrl(fImgURL);

      this.rImage = this.fbsImage.rearImageData
      let rImgURL = 'data:image/png;base64,'+item.rearImageData;
      this.rImage = this.sanitizer.bypassSecurityTrustUrl(rImgURL);
      //console.log(this.fImage)
    });
    //this.fImage = "F"+this.selectedItem.itemID?.trim()+".png"
    //this.rImage = "R"+this.selectedItem.itemID?.trim()+".png"
    //console.log(this.fImage)
    //this.populateFbsItemObj(this.selectedItem);
  }
  onRowUnselect($event: TableRowUnSelectEvent) {

  }




  returnToBatchList() {
    this.router.navigateByUrl('/batches');
  }
}
