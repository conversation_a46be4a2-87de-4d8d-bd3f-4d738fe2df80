.p-calendar .p-inputtext {
  font-family: var(--font-family), serif;
  font-feature-settings: var(--font-feature-settings, normal);
  font-size: 20px;
  color: #212529;
  background: #b2e3eb;
  padding: 0.5rem 0.75rem;
  border: 2px solid #ced4da;
  transition: background-color 0.15s, border-color 0.15s, box-shadow 0.15s;
  appearance: none;
  border-radius: 5px;
}

:host ::ng-deep .p-inputtext-sm .p-inputtext {
  font-size: 17px;
  width: 135px;
  height: 35px;
  font-weight: bold;
  padding: 0.4375rem 0.65625rem;
}

:host ::ng-deep .p-inputnumber-1 {
  margin-left: 15px;
  width: 115px;
  height:25px;
}

:host ::ng-deep .p-inputnumber-1 .p-inputnumber-input:enabled:focus,
:host ::ng-deep .p-inputnumber-1 .p-inputnumber-input{
  text-align: right;
  color: blue;
}

.ui-state-highlight {
  background: dodgerblue;
}

.highlight {
  background: #42A948
}
