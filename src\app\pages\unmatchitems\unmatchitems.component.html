<h4 class="font-bold flex justify-content-center mb-2" style="padding: 0">Unmatched FlexcCube Transactions</h4>
<p-toast/>
<div class="grid">
  <div class="col-6">
    <div class="card bg-blue-100" style="height:610px">
      <div class="col-fixed" style="width:200px; padding:0">FlexCube Transaction Date</div>
      <div class="field grid" style="margin-bottom: 0">
        <div class="col-fixed" style="padding:0; width: 170px">
          <p-calendar [(ngModel)]="procDate"
                      [showIcon]="true"
                      [maxDate]="maxDate"
                      dateFormat="dd M, yy"
                      class="font-bold block justify-content-center mb-2"
                      (onSelect)="onDateSelected($event)">
          </p-calendar>
        </div>
        <div class="col-fixed ml-3" style="padding:0; width: 240px">
          <button pButton label="Match Selected FBS Item"
                  style="margin-left:5px; width: 250px" (click)="updateMatchFBSItem()"></button>
        </div>
        <div class="col-fixed ml-3" style="padding:0; width: 140px">
          <button pButton label="Refresh List"
                  style="margin-left:25px; width: 150px" (click)="refreshList()"></button>
        </div>
      </div>
      <p-table [value]="fcTrans"
               dataKey="iflRefNo"
               [(selection)]="selectedTran"
               [expandedRowKeys]="expandedRows"
               (onRowExpand)="onRowExpand($event)"
               [tableStyle]="{'min-width': '35rem'}"
               [scrollable]="true"
               scrollHeight= "516px"
               styleClass="p-datatable-sm p-datatable-gridlines">
        <ng-template  pTemplate="header">
          <tr>
            <th class= "bg text-white" style="width: 2rem; height:.5rem"></th>
            <th class= "bg-black-alpha-70 text-white" style="width: 140px">FC Ref Key</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 85px">Transit</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 85px">Chq Ac No.</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 80px; text-align: center;">Chq Ser No.</th>
            <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 100px">Chq Amount</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-unmatchItem let-expanded="expanded">
          <!--tr [pSelectableRow]="dupChqItem"-->
          <tr>
            <td>
              <button type="button" pButton pRipple
                      class="p-button-text p-button-rounded p-button-plain"
                      [pRowToggler]="unmatchItem"
                      (click)="buttonClick()"
                      [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'">
              </button>
            </td>
            <td style="height:25px">{{unmatchItem.iflRefNo}}</td>
            <td style="height:25px">{{unmatchItem.routeNo}}</td>
            <td style="height:25px">{{unmatchItem.chqAcct}}</td>
            <td style="text-align: center; height:25px">{{unmatchItem.chqSerNo}}</td>
            <td style="text-align: right; height:25px" inputmode="decimal">{{unmatchItem.chqAmount | currency}}</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="rowexpansion" let-fbsItem>
          <tr>
            <td colspan="6" class="bg-cyan-300">
              <div class="pt-0 pb-0">
                <p-table [value]="UMFBSItems"
                         dataKey="iflRefNo"
                         selectionMode="single"
                         (onRowSelect)="onRowSelect($event)"
                         (onRowUnselect)="onRowUnselect($event)"
                         styleClass="p-datatable-sm p-datatable-gridlines">
                         <!--[(selection)]="selectedChqItem"-->
                  <ng-template pTemplate="header">
                    <tr>
                      <!--th style="width: 2rem">
                        <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                      </th-->
                      <!--th class= "bg-black-alpha-50 text-white" style="width: 2rem"></th-->
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Item ID</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Route No.</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Chq AC No.</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 85px">Chq SerNo.</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="width: 80px">Chq Date</th>
                      <th class= "bg-bluegray-300 text-blue-900" style="text-align: right; width: 100px">Chq Amount</th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-UnmatchFBSItem>
                    <tr [pSelectableRow]="UnmatchFBSItem">
                      <!--td> <a (click)="selectedRowItem(UnmatchFBSItem)">{{ UnmatchFBSItem.itemID}}</a></td-->
                      <td>{{ UnmatchFBSItem.itemID }}</td>
                      <td>{{ UnmatchFBSItem.routeNo }}</td>
                      <td>{{ UnmatchFBSItem.chqAcNo }}</td>
                      <td>{{ UnmatchFBSItem.serNo }}</td>
                      <td>{{ UnmatchFBSItem.chqDate }}</td>
                      <td style="text-align: right" inputmode="decimal">{{UnmatchFBSItem.chqAmount | currency}}</td>
                    </tr>
                  </ng-template>
                  <!--ng-template pTemplate="emptymessage">
                    <tr>
                      <td colspan="6">There are no records found</td>
                    </tr>
                  </ng-template-->
                </p-table>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="col-6">
    <div class="card bg-blue-100" style="height:610px">
      <div class="field grid" style="margin-bottom: 0">
        <div class="col-fixed ml-2" style="width:180px; font-weight: bold">Batch Id</div>
        <div class="col-fixed" style="width:250px; font-weight: bold">Item Id</div>
        <div class="col-fixed" style="width:145px; font-weight: bold">Batch Scan Date</div>
        <div class="col-fixed" style="width:125px; font-weight: bold">Processing Date</div>
      </div>
      <div class="field grid">
        <input type="text" pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedItem.batchID" style="margin-left:10px; width:120px; height:25px"/>
        <input pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedItem.itemID" style="margin-left:60px; width:150px; height:25px"/>
        <input pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedItem.fbsDate" style="margin-left:95px; width:120px; height:25px"/>
        <input pInputText class="p-inputtext" [disabled]=true [(ngModel)]="selectedItem.procDate" style="margin-left:25px; width:120px; height:25px"/>
      </div>

      <div class="card p-fluid" style="margin-top: 20px; margin-bottom: .25rem; padding-top: .5rem; padding-bottom: .5rem">
        <div class="field grid" style="margin-bottom: 0;margin-top: 15px">
          <div class="col-fixed" style="width:80px; font-weight: bold">Bank No</div>
          <div class="col-fixed" style="width:130px; font-weight: bold">Routing No.</div>
          <div class="col-fixed" style="width:145px; font-weight: bold">Account No.</div>
          <div class="col-fixed" style="width:125px; font-weight: bold">CHQ Date</div>
          <div class="col-fixed" style="width:100px; font-weight: bold">Serial No.</div>
          <div class="col-fixed" style="width:120px; font-weight: bold; text-align:right">Amount</div>
        </div>

        <div class="field grid">
          <input pInputText class="p-inputtext" style="margin-left:5px; width:50px; height:25px" [(ngModel)]="selectedItem.bankNo"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:100px; height:25px" [(ngModel)]="selectedItem.routeNo"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:110px; height:25px" [(ngModel)]="selectedItem.chqAcNo"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:100px; height:25px" [disabled]=true [(ngModel)]="selectedItem.chqDate"/>
          <input pInputText class="p-inputtext" style="margin-left:30px; width:85px; height:25px" [(ngModel)]="selectedItem.serNo"/>
          <p-inputNumber class="p-inputnumber-1" [disabled]=true [(ngModel)]="selectedItem.chqAmount" mode="decimal" [minFractionDigits]="2"></p-inputNumber>
         </div>

        <div class="field grid" style="margin-bottom: 0; margin-top: 20px">
          <div class="col-fixed" style="width:230px; font-weight: bold">Beneficiary A/C No.</div>
          <div class="col-fixed" style="width:230px; font-weight: bold">MICR - On Us</div>
          <div class="col-fixed" style="width:150px; font-weight: bold">MICR - Aux On Us</div>
        </div>

        <div class="field grid" style="margin-bottom: 5px">
          <input pInputText class="p-inputtext" style="margin-left:5px; width:150px; height:25px" [(ngModel)]="selectedItem.custAcNo"/>
          <input pInputText class="p-inputtext" style="margin-left:75px; width:200px; height:25px" [disabled]=true [(ngModel)]="selectedItem.onUs"/>
          <input pInputText class="p-inputtext" style="margin-left:35px; width:235px; height:25px" [disabled]=true [(ngModel)]="selectedItem.auxOnUs"/>
        </div>
        <div class="col-12">
          <button pButton label="Save Changes" icon="pi pi-chevron-left"
                  (click)="saveFBSItem()" style="margin-bottom:15px; width: 150px"></button>
        </div>
        <div class="col-12" style="padding: .25rem 0 0 0">
          <p-tabView>
            <p-tabPanel header="Chq Fromt Image">
              <p-image [src]='fImage' alt="Front Image" height = "230px" width="650px" [preview] = "true"></p-image>
            </p-tabPanel>
            <p-tabPanel header="Chq Rear Image">
              <p-image [src]='rImage' alt="Rear Image" height= 230px width="650px" [preview] = "true" previewImageSizes="40%"></p-image>
            </p-tabPanel>
          </p-tabView>
        </div>
      </div>
    </div>
  </div>
</div>
