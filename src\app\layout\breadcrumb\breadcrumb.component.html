<div class="layout-breadcrumb-container">
  <nav class="layout-breadcrumb">
    <ol>
      <li><i class="pi pi-home"></i></li>
      @for (item of breadcrumbs$ | async; track item; let last = $last) {
        <li><i class="pi pi-angle-right"></i></li>
        <li><span>{{item.label}}</span></li>
      }
    </ol>
  </nav>
  <div class="layout-breadcrumb-buttons">
    <button pButton pRipple type="button" icon="pi pi-cloud-upload" class="p-button-rounded p-button-text p-button-plain"></button>
    <button pButton pRipple type="button" icon="pi pi-bookmark" class="p-button-rounded p-button-text p-button-plain"></button>
    <button pButton pRipple type="button" icon="pi pi-power-off" class="p-button-rounded p-button-text p-button-plain"></button>
  </div>
</div>
