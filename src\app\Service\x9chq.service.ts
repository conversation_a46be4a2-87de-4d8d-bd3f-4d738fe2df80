import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../API/batch";
import {X9ChqModel} from "../API/x9cheques";

@Injectable({
  providedIn: 'root'
})
export class X9chqService {

  constructor(private http: HttpClient) { }

  getX9ChqData(fbsDate: string) {
    return this.http.get<X9ChqModel[]>('http://localhost:8080/api/X937Chqs/'+fbsDate);
  }

}
