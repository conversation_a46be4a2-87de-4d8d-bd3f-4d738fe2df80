<h4 class = "font-bold flex justify-content-center" >BATCH - ITEM LIST</h4>
<p-toast></p-toast>
<button pButton pRipple label="Batch List" icon="pi pi-chevron-left" (click)="returnToBatchList()"
        style="margin-bottom: 15px; margin-left:10px"></button>
<button class ="inline" pButton pRipple label="Print Items Report" icon="pi pi-print" (click)="batchDetailsReport()"
        style="margin-bottom: 15px; margin-left:20px"></button>

<div class="grid">
  <div class="col-12 card bg-blue-100" style="height: 545px; width: 98%; margin-left: 10px">
    <p-table [value]="nchItems"
             dataKey="recID"
             selectionMode="single"
             [(selection)]="selectedItem"
             [tableStyle]="{'min-width': '30rem'}"
             [scrollable]="true"
             scrollHeight="525px"
             styleClass="p-datable-sm p-datatable-gridlines">
      <ng-template pTemplate="header">
        <tr>
          <th class="bg-black-alpha-70 text-white" style="width: 2.5rem">BATCH ID</th>
          <th class="bg-black-alpha-70 text-white" style="width: 2rem">T-CODE</th>
          <th class="bg-black-alpha-70 text-white" style="width: 3rem">ROUTE NO</th>
          <th class="bg-black-alpha-70 text-white"  style="width: 8rem">A/C NUMBER</th>
          <th class="bg-black-alpha-70 text-white" style="width: 8rem">A/C TYPE</th>
          <th class="bg-black-alpha-70 text-white" style="width: 8rem">INDV. ID</th>
          <th class="bg-black-alpha-70 text-white" style="width: 9rem">INDV. NAME</th>
          <th class="bg-black-alpha-70 text-white" style="text-align: right; width: 8rem">ITEM AMOUNT</th>
          <th class="bg-black-alpha-70 text-white"  style="width: 5rem">TRACE NO.</th>
          <th class="bg-black-alpha-70 text-white"  style="width: 5rem">RET CODE</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-nchItem>
        <tr [pSelectableRow]="nchItem" [ngStyle]="{height: '20px'}">
          <td>{{nchItem.batchId}}</td>
          <td>{{nchItem.tranCode}}</td>
          <td>{{nchItem.routeNo}}</td>
          <td>{{nchItem.acctNo}}</td>
          <td>{{nchItem.acctType}}</td>
          <td>{{nchItem.indvId}}</td>
          <td>{{nchItem.indvName}}</td>
          <td style="text-align: right" inputmode="decimal">{{nchItem.entryAmt | currency}}</td>
          <td>{{nchItem.traceNbr}}</td>
          <td>{{nchItem.retRCode}}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog header="Processing, pleas wait.."
          [(visible)]="showSpinnerDialog"
          [modal]="true"
          [style]="{ width: '20rem' }"
          position="center">
  <div class="card flex justify-content-center">
    <p-progressSpinner
      styleClass="w-4rem h-4rem"
      strokeWidth="8"
      fill="var(--surface-ground)"
      animationDuration=".5s" />
  </div>
</p-dialog>



