import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {itemModel} from "../../interfaces/items";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {CurrencyPipe, formatDate, NgStyle} from "@angular/common";
import {TableModule, TableRowSelectEvent} from "primeng/table";
import {ConfirmationService, MessageService} from "primeng/api";
import {AchItemService} from "../../Service/achitem.service";
import {DropdownChangeEvent, DropdownModule} from "primeng/dropdown";
import {DialogModule} from "primeng/dialog";
import {CheckboxChangeEvent, CheckboxModule} from "primeng/checkbox";
import {Ripple} from "primeng/ripple";
import {RespMessage} from "../../interfaces/respmesg";
import {ToastModule} from "primeng/toast";
import {GenericService} from "../../Service/generic.service";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {ConfirmDialogModule} from "primeng/confirmdialog";

@Component({
  selector: 'app-retitems',
  standalone: true,
  imports: [
    CalendarModule,
    FormsModule,
    CurrencyPipe,
    TableModule,
    NgStyle,
    DropdownModule,
    DialogModule,
    CheckboxModule,
    ToastModule,
    ProgressSpinnerModule,
    ConfirmDialogModule,
  ],
  templateUrl: './retitems.component.html',
  styleUrl: './retitems.component.css',
  providers: [ConfirmationService, MessageService]
})
export class RetitemsComponent implements OnInit, OnDestroy {
  retCodes =[
    {label: 'R01 - INSUFFICIENT FUNDS', value: 'R01'},
    {label: 'R02 - ACCOUNT CLOSED', value: 'R02'},
    {label: 'R03 - NO ACCOUNT/UNABLE TO LOCATE ACCOUNT', value: 'R03'},
    {label: 'R04 - INVALID ACCOUNT NUMBER', value: 'R04'},
    {label: 'R05 - UNAUTHORIZED DEBIT', value: 'R05'},
    {label: 'R06 - RETURNED PER ODFI REQUEST', value: 'R06'},
    {label: 'R07 - AUTHORIZATION REVOKED', value: 'R07'},
    {label: 'R08 - PAYMENT STOPPED', value: 'R08'},
    {label: 'R09 - UNCOLLECTED FUNDS', value: 'R09'},
    {label: 'R10 - CUSTOMER ADVISES NOT AUTHORIZED', value: 'R10'},
    {label: 'R11 - CHECK TRUNCATION ENTRY RETURNED', value: 'R11'},
    {label: 'R12 - ACCOUNT SOLD TO ANOTHER DFI', value: 'R12'},
    {label: 'R14 - REPRESENTATIVE PAYEE DECEASED', value: 'R14'},
    {label: 'R15 - BENEFICIARY OR ACCOUNT HOLDER DECEASED', value: 'R15'},
    {label: 'R16 - ACCOUNT FROZEN', value: 'R16'},
    {label: 'R20 - NON-TRANSACTION AMOUNT', value: 'R20'},
    {label: 'R21 - INVALID COMPANY IDENTIFICATION', value: 'R21'},
    {label: 'R22 - INVALID INDIVIDUAL ID NUMBER', value: 'R22'},
    {label: 'R24 - DUPLICATE ENTRY', value: 'R24'},
  ];

  rtnItems!: itemModel[];
  selectedItem!: itemModel;
  selRetItem!: itemModel;
  achDate: Date = new Date;
  maxDate: Date = new Date;
  selectedDate: Date = new Date;
  dateString: string = '';
  retDateString: string = '';
  previousDate: Date = new Date;
  displayReturnCodes: boolean = false;
  rtnItemCode: any = '';
  isChecked: boolean = false;
  httpRslt: any
  itemRecId: any;
  itemCount: number = 0;
  checkboxDisabled: boolean = false;
  respMsg!: RespMessage;
  showSpinnerDialog: boolean = false;
  displayDateDialog: boolean = false;
  achRetDate: Date = new Date;
  selRetDate: Date = new Date;
  retDateSelected: boolean = false;

  constructor(private achItemService: AchItemService,
              private genericService: GenericService,
              private messageService: MessageService,
              private confirmationService: ConfirmationService) {

  }

  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.fetchRetItems(this.dateString);
  }

  onSelectedDate(event: Date) {
    this.selectedDate = event;
    //console.log("Ach Date: "+this.achDate.toDateString())
    //console.log("Sel Date: "+this.selectedDate.toDateString())

    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    if (this.achDate.toDateString() != this.previousDate.toDateString()) {
      this.fetchRetItems(this.dateString);
      this.previousDate = this.achDate;
    } else {
      this.messageService.add({
        severity: 'info',
        "summary": 'Date already selected',
        "detail": this.achDate.toDateString()
      });
      //console.log("Date selected... [" + this.selectedDate.toDateString() + "]")
    }
  }

  fetchRetItems(itemACHDate: string) {
    this.achItemService.fetchNCHItemData(itemACHDate).subscribe( items =>{
      this.rtnItems = items;
      this.itemCount = items?.length || 0;
      for (let i = 0; i < this.rtnItems.length; i++) {
        this.rtnItems[i].checkBox = this.rtnItems[i].retInd === 1;
      }
    })
  }

  ngOnDestroy(): void {
  }

  chkBoxClick(retItem: itemModel) {
    this.selRetItem = retItem;
    this.selectedItem = retItem;
    //console.log("cbOnClick: "+retItem.indvName)
    console.log("cbOnClick :: Checked = "+this.isChecked)
    if (this.isChecked)
      this.displayReturnCodes = true
    else {
      this.selectedItem.retRCode = ''
      this.selectedItem.retInd = 0
      this.itemRecId = this.selectedItem.recID;
      this.httpRslt = this.achItemService.updateRetItemDet(this.itemRecId, this.selectedItem.retRCode, this.selectedItem.retInd)
        .subscribe(resp => {
          this.respMsg = resp
          //console.log("Message:    "+this.respMsg.message)
          //console.log("HttpStatus: "+this.respMsg.status)
          this.messageService.add({ severity: 'info',
            summary: this.respMsg.message});
        })
    }
    console.log("Return Ind: "+retItem.retInd+", Return Reason Code: "+retItem.retRCode)
  }

  chkBoxChange(event: CheckboxChangeEvent) {
    this.isChecked = event.checked;
    //console.log("cbOnChangeEvent fired.."+event.checked)
  }

  drpDwnChange(retItem: itemModel) {
    this.selectedItem = retItem
    //console.log("DropDownChangeEvent: Indv Name = "+this.selectedItem.indvName)
    //console.log("DropDownChangeEvent: RetItem RCode = "+this.rtnItemCode)
    //console.log("DropDownChangeEvent: Checked = "+this.isChecked)
    this.displayReturnCodes = false
    //retItem.checkBox = false
    this.selectedItem.retRCode = this.rtnItemCode
    this.selectedItem.retInd = 1
    this.itemRecId = this.selectedItem.recID;
    this.httpRslt = this.achItemService.updateRetItemDet(this.itemRecId, this.selectedItem.retRCode, this.selectedItem.retInd)
      .subscribe(resp => {
        this.respMsg = resp
        //console.log("Message:    "+this.respMsg.message)
        //console.log("HttpStatus: "+this.respMsg.status)
      })
  }

  drpDwnHide(retItem: itemModel) {
    //console.log("Drp Down HideEvent: Indv Name = "+retItem.indvName)
  }

  diagHide(retItem: itemModel) {
    if (this.rtnItemCode === '') {
      retItem.checkBox = false
      //console.log("Dialog Hide " + this.rtnItemCode)
    }
  }

  diagShow() {
    this.rtnItemCode = ''
  }

  onRowSelect(event: TableRowSelectEvent) {
    this.selectedItem = event.data
    //console.log(this.selectedItem)
  }

  generateRetFile() {
    this.showSpinnerDialog=true
    //console.log("Ach Date: "+this.dateString)
    //console.log("Ret Date: "+this.retDateString)
    this.httpRslt = this.genericService.generateReturnFile(this.dateString, this.retDateString).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog=false
      //console.log("Message:    "+this.respMsg.message)
      //console.log("HttpStatus: "+this.respMsg.status)
      this.messageService.add({ severity: 'info',
        summary: this.respMsg.message});
      this.fetchRetItems(this.dateString);
    })
  }

  printReportRetRep01() {
    this.showSpinnerDialog=true
    this.httpRslt = this.genericService.generateReportRetRep01(this.dateString).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog=false
      this.messageService.add({ severity: 'info',
        summary: this.respMsg.message});
      this.fetchRetItems(this.dateString);
    })
  }

  printReportRetRep02() {
    this.showSpinnerDialog=true
    this.httpRslt = this.genericService.generateReportRetRep02(this.dateString).subscribe(resp => {
      this.respMsg = resp
      this.showSpinnerDialog=false
      this.messageService.add({ severity: 'info',
        summary: this.respMsg.message});
      this.fetchRetItems(this.dateString);
    })
  }

  showDateDialog() {
    this.displayDateDialog=true

  }

  confirmRet() {
    this.confirmationService.confirm({
      message: 'Are you sure that you want to proceed?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      accept: () => {
        this.generateRetFile()
      },
    });
  }

  onSelectedRetDate(event: Date) {
    this.selRetDate = event;
    //console.log("Ach Ret Date: "+this.achRetDate.toDateString())
    //console.log("Sel Ret Date: "+this.selRetDate.toDateString())
    this.retDateSelected = true
    this.retDateString = formatDate(this.selRetDate, "yyyy-MM-dd", "en-US")
    //console.log(this.retDateString)
    this.displayDateDialog=false
  }

  dateDialogShow() {
    this.achRetDate = this.selectedDate
    this.retDateSelected = false
  }

  dateDialogHide(selectedItem: itemModel) {
    if (!this.retDateSelected) {
      this.messageService.add({
        severity: 'info',
        "summary": 'Please Select a Date',
        "detail": 'No Date was Selected'
      });
    } else {
      this.confirmRet()
    }
  }
}
