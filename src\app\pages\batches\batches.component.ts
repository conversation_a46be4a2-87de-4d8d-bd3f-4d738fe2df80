import {Component, inject, OnInit, OnDestroy} from '@angular/core';
import {TableModule} from "primeng/table";
import {SelectButtonModule} from "primeng/selectbutton";
import {MessageService} from "primeng/api";
import {ToastModule} from "primeng/toast";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {HttpClient} from "@angular/common/http";
import {BatchService} from "../../Service/batch.service";
import {batchModel} from "../../API/batch";
import {CurrencyPipe, formatDate} from "@angular/common";
import {Router} from "@angular/router";


@Component({
  selector: 'app-batches',
  standalone: true,
  imports: [TableModule, SelectButtonModule, ToastModule, CalendarModule, FormsModule, CurrencyPipe],
  templateUrl: './batches.component.html',
  styleUrl: './batches.component.css',
})

export class BatchesComponent implements OnInit, OnDestroy{
  private http = inject(HttpClient);
  batchDate: Date = new Date;
  previousDate: Date = new Date;
  selectedDate: Date = new Date;
  batches!: batchModel[];
  selectedBatch!: batchModel;
  maxDate: Date = new Date;
  dateString: string = '';
  noOfBatches: number = 0;
  batchID: string = '';
  highlighted: any;

  constructor(private batchService: BatchService,
              private router: Router,
              private messageService: MessageService) {
  }

  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.Loadbatches(this.dateString);
  }

  ngOnDestroy() {
    //console.log('Batches Component :: ngOnDestroy')
  }

  Loadbatches(fbsDate: string) {
    //debugger
    this.batchService.getBatchData(fbsDate).subscribe(item => {
      this.noOfBatches = item.length;
      this.batches = item;
      this.messageService.add({ severity: 'info',
        summary: this.noOfBatches+' Batches loaded'});
      //console.log(this.noOfBatches);
    })
  }


  selectedRow(batchItem: any) {
    //console.log("batchItem: "+batchItem.batchId)
    this.highlighted = batchItem
    //console.log("highlighted: "+this.highlighted.batchId)
    this.batchID = batchItem.batchId;
    //console.log(batchItem.batchId+" === "+this.highlighted.batchId)
    this.router.navigateByUrl('fbsitems/'+this.batchID);
  }


  onRowSelect(event: any) {
    //debugger;
    this.batchID = event.data.batchId;
    console.log("RowSelect..."+event.data.batchId)
/*    this.messageService.add({ severity: 'info',
      summary: 'Batch Selected',
      detail: event.data.batchId });
 */
    //this.router.navigate(['fbsitems' ])
    //this.router.navigateByUrl('fbsitems/'+this.batchID);

  }

  onRowUnselect(event: any) {
    console.log("RowUnselect..." + event.data.batchId)
    //this.messageService.add({ severity: 'info', summary: 'Batch Unselected', detail: event.data.batchId});
  }

  onDateSelected(event: Date) {
    this.selectedDate = event;
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    if (this.batchDate.toDateString() != this.previousDate.toDateString()) {
      this.Loadbatches(this.dateString);
      this.previousDate = this.batchDate;

    } else {
      this.messageService.add({
        severity: 'info',
        "summary": 'Date already selected',
        "detail": this.batchDate.toDateString()
      });
      //console.log("Date selected... [" + this.selectedDate.toDateString() + "]")
    }
  }

  cbOnClick(batchItem: any) {
    this.batchID = batchItem.batchId;
    console.log("cbOnClick triggered.."+this.batchID)

  }
}
