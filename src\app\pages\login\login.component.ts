import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {LayoutService} from "../../layout/layout.service";
import {InputTextModule} from "primeng/inputtext";
import {ButtonModule} from "primeng/button";
import {RippleModule} from "primeng/ripple";
import {Router, RouterLink, RouterOutlet} from "@angular/router";
import {BnNgIdleService} from "bn-ng-idle";
import {MessageService} from "primeng/api";
import {AuthService} from "../../Service/auth.service";
import {ToastModule} from "primeng/toast";
import {PasswordModule} from "primeng/password";
import {User} from "../../Models/user";
import {FormsModule} from "@angular/forms";

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [InputTextModule,
    ButtonModule, RippleModule, RouterLink, RouterOutlet,
    PasswordModule, ToastModule, FormsModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit, OnDestroy{
  rememberMe: boolean = false;
  minute = 60
  userPassword!: string;
  userName!: string;

  constructor(private layoutService: LayoutService,
              private messageService: MessageService,
              private authService: AuthService,
              private router: Router,
              private bnIdle: BnNgIdleService,) {

  }

  get dark(): boolean {
    return this.layoutService.config().colorScheme !== 'light';
  }

  onLogin() {
    let user: User | undefined;
    user = this.authService.login(this.userName, this.userPassword);
    //console.log(user?.name+", "+user?.userRole)
    if (this.authService.IsAuthenticated()) {
      this.startSessTimer()
      this.router.navigateByUrl('/dashboard').then(r => '/')
    }else {
      this.messageService.add({ severity: 'info', summary: 'Login Details', detail: 'Invalid Credentials!' });
    }
  }

  startSessTimer() {
    //console.log(formatDate(Date.now(), 'dd-MM-yyyy hh:mm:ss ', 'en-US')+"Session timer started..")
    this.bnIdle.startWatching(10*this.minute).subscribe((isTimeout: boolean) => {
      if (isTimeout) {
        //console.log(formatDate(Date.now(), 'dd-MM-yyyy hh:mm:ss', 'en-US')+" Session has expired.."+isTimeout);
        this.router.navigate(['/']).then(r => '/' );
        this.bnIdle.stopTimer();
        this.userName = '';
        this.userPassword = '';
        //console.log(formatDate(Date.now(), 'dd-MM-yyyy hh:mm:ss ', 'en-US')+"Session timer stopped..")
      }
    })
  }

  ngOnDestroy(): void {
  }

  ngOnInit(): void {
  }
}
