import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {batchModel} from "../interfaces/batch";
import {FilesModel} from "../interfaces/files";
import {isDevMode} from "@angular/core";
import {environment} from "../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class AchFilesService {
  urlStr: string = '';
  //baseUrl: string;

  constructor(private http: HttpClient) {
    // if (isDevMode()) {
    //   this.baseUrl = "http://localhost:8080/api/";
    // } else {
    //   this.baseUrl = "http://***********:8080/api/"
    // }
  }

  fetchOBACHFiles(achDate: string) {
    this.urlStr = environment.baseUrl+'achOBFileList/' + achDate
    //console.log(this.urlStr)
    return this.http.get<FilesModel[]>(this.urlStr);
  }

  fetchEPayFiles(achDate: string) {
    this.urlStr = environment.baseUrl+'achEPayFiles/' + achDate
    //console.log(this.urlStr)
    return this.http.get<FilesModel[]>(this.urlStr);
  }

  fetchCruiseFiles(achDate: string) {
    this.urlStr = environment.baseUrl+'achCruiseFiles/' + achDate
    //console.log(this.urlStr)
    return this.http.get<FilesModel[]>(this.urlStr);
  }
}
