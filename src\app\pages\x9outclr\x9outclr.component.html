<h4 class="font-bold flex justify-content-center mb-2" style="padding: 0">X9 Out Clearing Cheques</h4>
<p-toast/>
<p-dialog/>
<div class="grid" style="padding-top:10px">
  <div class="col-6">
    <div class="card bg-blue-100" style="height:610px; padding-top:10px">
      <div class="flex-auto">
        <div><label class="font-bold block" style="font-size: 15px; margin-bottom:5px; padding: 0" >FBS Processing Date</label></div>
        <div class="inline-flex bottom-0">
          <p-calendar [(ngModel)]="procDate"
                      [showIcon]="true"
                      [maxDate]="maxDate"
                      dateFormat="dd M, yy"
                      class="p-inputtext-sm block mb-1"
                      [style]="{'font-size': 'large'}"
                      (onSelect)="onDateSelected($event)">
          </p-calendar>
          <button class = "inline" pButton pRipple label="Refresh Cheque List" icon="pi pi-refresh"
                  style="margin-left:10px; margin-bottom:5px" (click)="refreshList()"></button>
          <button class = "inline" pButton pRipple label="Generate X9 File"
                  style="margin-left:10px; margin-bottom:5px" (click)="generateX9File()"></button>
          <button class = "inline" pButton pRipple label="Print Cheques Report"
                  style="margin-left:10px; margin-bottom:5px" (click)="printX9OutClrReport1()"></button>
        </div>
      </div>
      <p-table [value]="x9Chqs"
               dataKey="iflRefKey"
               [(selection)]="selectedX9Chq"
               selectionMode="single"
               (onRowSelect)="onRowSelect($event)"
               (onRowUnselect)="onRowUnselect($event)"
               [tableStyle]="{'min-width': '35rem'}"
               [scrollable]="true"
               (onHeaderCheckboxToggle)="headerCheckboxToggle($event)"
               scrollHeight= "520px"
               styleClass="p-datatable-sm p-datatable-gridlines">
        <ng-template  pTemplate="header">
          <tr>
            <th class= "bg-black-alpha-50 text-white" style="width: 2rem">
              <p-tableHeaderCheckbox/>
            </th>
            <th class= "bg-black-alpha-70 text-white" style="width: 150px">FlexCube Ref No.</th>
            <th class= "bg-black-alpha-70 text-white" style="width: 160px">FBS Item ID</th>
            <th class= "bg-black-alpha-70 text-white"style="width: 110px">Ben. A/C No</th>
            <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 130px">Cheque Amount</th>
            <!--th class= "bg-black-alpha-70 text-white" style="width: 75px">Branch</th-->
            <th class= "bg-black-alpha-70 text-white" style="width: 90px">Status Ind</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-chequeItem>
          <tr [class.ui-state-highlight]="chequeItem === highlighted">
            <td><input type="checkbox"
                       [(ngModel)]="chequeItem.isSelected"
                       [value]="chequeItem"
                       (click)="cbOnClick(chequeItem)"
                       (change)="OnChangeEvent(chequeItem)"
                       style="width: 20px; height: 19px; margin-left: 0"
            /></td>
            <td><a (click)="selectedRow(chequeItem)"> {{chequeItem.iflRefKey}}</a></td>
            <td><a (click)="selectedRow(chequeItem)"> {{chequeItem.iflFBSItemId}}</a></td>
            <td>{{chequeItem.iflBenAcctNo}}</td>
            <td style="text-align: right" inputmode="decimal">{{chequeItem.iflChqAmount | currency}}</td>
            <td style="text-align: center">{{chequeItem.fbsX937Ind}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="col-6">
    <div class="card bg-blue-100" style="height:610px">
      <div class="card p-fluid">
        <div class="field grid">
          <div class="col-fixed" style="width:80px">Batch Id:</div>
          <div class="col-3" style="font-weight: bold">{{ selectedChq.fbsItemId?.substring(0,14) }}</div>
          <div class="col-fixed" style="width:100px">Branch Code:</div>
          <div class="col-2" style="font-weight: bold">{{ selectedChq.fbsChqRouteNo }}</div>
          <div class="col-fixed" style="width:100px">Batch Date:</div>
          <div class="col-2" style="font-weight: bold">{{dateString}}</div>
        </div>
        <div class="field grid">
          <div class="col-fixed" style="width:80px">Item Id:</div>
          <div class="col-3" style="font-weight: bold">{{selectedChq.fbsItemId}}</div>
          <div class="col-fixed" style="width:100px">Beneficiary #:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.iflBenAcctNo}}</div>
          <div class="col-fixed" style="width:100px">Amount:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.fbsChqAmount | currency}}</div>
        </div>
        <div class="field grid">
          <div class="col-fixed" style="width:80px">Route No.:</div>
          <div class="col-3" style="font-weight: bold">{{selectedChq.fbsChqRouteNo}}</div>
          <div class="col-fixed" style="width:100px">Cheque No.:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.fbsChqSerNo}}</div>
          <div class="col-fixed" style="width:100px">Chq. Date:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.fbsChqDate?.substring(0,10)}}</div>
        </div>
        <div class="field grid">
          <div class="col-fixed" style="width:80px">Bank No.:</div>
          <div class="col-3" style="font-weight: bold">{{selectedChq.fbsChqRouteNo}}</div>
          <div class="col-fixed" style="width:100px">Chq A/C No.:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.fbsChqAcctNo}}</div>
          <div class="col-fixed" style="width:100px">Proc. Date:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.fbsProcDate?.substring(0,10)}}</div>
        </div>
        <div class="field grid">
          <div class="col-fixed" style="width:80px">Status:</div>
          <div class="col-3" style="font-weight: bold">{{selectedChq.chqStatus}}</div>
          <div class="col-fixed" style="width:100px">X9 Filename.:</div>
          <div class="col-2" style="font-weight: bold">{{selectedChq.x9Filename}}</div>
        </div>
      </div>
      <p-tabView>
        <p-tabPanel header="Chq Fromt Image">
          <p-image [src]='fImage' alt="Front Image" height = "260px" width="680px" [preview] = "true"></p-image>
          <!--img [src]='fImage' alt="Front Image" height = "230px" width="600px"-->
        </p-tabPanel>
        <p-tabPanel header="Chq Rear Image">
          <!--img [src]='rImage' alt="Rear Image" height = "230px" width="600px"-->
          <p-image [src]='rImage' alt="Rear Image" height= 260px width="680px" [preview] = "true" previewImageSizes="40%"></p-image>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>
</div>

<p-dialog header="Processing, pleas wait.."
          [(visible)]="showSpinnerDialog"
          [modal]="true"
          [style]="{ width: '20rem' }"
          position="center">
  <div class="card flex justify-content-center">
    <p-progressSpinner
      styleClass="w-4rem h-4rem"
      strokeWidth="8"
      fill="var(--surface-ground)"
      animationDuration=".5s" />
  </div>
</p-dialog>
