import {Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation} from '@angular/core';
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {HttpClient, HttpParams} from "@angular/common/http";
import {CurrencyPipe, formatCurrency, formatDate} from "@angular/common";
import {MessageService} from "primeng/api";
import {DupitemService} from "../../Service/dupitem.service";
import {dupitemsModel} from "../../API/dupitems";
import {TableModule, TableRowExpandEvent, TableRowSelectEvent, TableRowUnSelectEvent} from "primeng/table";
import {RippleModule} from "primeng/ripple";
import {fbsItemModel} from "../../API/fbsItem";
import {FbsitemsService} from "../../Service/fbsitems.service";
import {InputMaskModule} from "primeng/inputmask";
import {InputNumberModule} from "primeng/inputnumber";
import {DropdownModule} from "primeng/dropdown";
import {MultiSelectModule} from "primeng/multiselect";
import {InputTextareaModule} from "primeng/inputtextarea";
import {ImageModule} from "primeng/image";
import {TabViewModule} from "primeng/tabview";
import {InputTextModule} from "primeng/inputtext";
import {fbsImageModel} from "../../API/fbsImage";
import {DomSanitizer} from "@angular/platform-browser";
import {ToastModule} from "primeng/toast";
import {ExpandedRows} from "../../API/expandedrows";


@Component({
  selector: 'app-dupitems',
  standalone: true,
  imports: [
    CalendarModule,
    FormsModule,
    CurrencyPipe,
    TableModule,
    RippleModule,
    InputMaskModule,
    InputNumberModule,
    DropdownModule,
    MultiSelectModule,
    InputTextareaModule,
    ImageModule,
    TabViewModule,
    InputTextModule,
    ToastModule,
  ],
  templateUrl: './dupitems.component.html',
  styleUrl: './dupitems.component.css',
})

export class DupitemsComponent implements OnInit, OnDestroy{
  private http = inject(HttpClient);
  maxDate: Date = new Date;
  procDate: Date = new Date;
  dupChqs!: dupitemsModel[];
  selectedChq!: dupitemsModel;
  previousDate: Date = new Date;
  selectedDate: Date = new Date;
  dateString: string = '';
  dupFBSItems!: fbsItemModel[];
  selectedItem!: fbsItemModel
  selectedFbsItem: fbsItemModel = { selected: false };
  expandedRows: ExpandedRows = {};
  isExpanded: boolean = false;
  fbsImage: fbsImageModel = {}
  fImage?: any;
  rImage?: any;
  batchID: any;
  itemID: any;
  highlighted: any;
  rowIndex: number | undefined = 99;
  fbsItemId: any;
  httpRslt: any;

  constructor(private dupItemService: DupitemService,
              private fbsitemsService: FbsitemsService,
              private sanitizer: DomSanitizer,
              private messageService: MessageService) {
  }

  ngOnDestroy(): void {
  }

  ngOnInit(): void {
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.loadDupItems(this.dateString)
  }


  refreshDupList() {
    this.loadDupItems(this.dateString)
  }

  loadDupItems(procDate: string) {
    this.dupItemService.fetchDupItems(procDate).subscribe(item => {
      this.dupChqs = item;
      //console.log(this.dupChqs)
    })
  }

  onDateSelected(event: Date) {
    this.selectedDate = event;
    this.dateString = formatDate(this.selectedDate, "yyyy-MM-dd", "en-US")
    this.loadDupItems(this.dateString)
  }

  onRowSelect(event: TableRowSelectEvent) {
    //debugger
    //console.log("onRowSelect triggered..."+event.data.itemID)
    //this.selectedFbsItem.selected = true;
    //let arrayIndex = this.dupFBSItems.findIndex((item) => item.itemID == event.data.itemID)
    let arrayIndex = this.dupFBSItems.findIndex((item) => item.itemID == event.data.itemID)
    if(arrayIndex >= 0) {
        this.dupFBSItems[arrayIndex].selected = true;
        //console.log(arrayIndex+"-"+this.dupFBSItems[arrayIndex].itemID+", Selected? "+this.dupFBSItems[arrayIndex].selected)
    }
  }

  onRowUnselect(event: TableRowUnSelectEvent) {
    //console.log("onRowUnselect triggered...")
    //console.log("Checked? "+this.selectedFbsItem.selected)
    let arrayIndex = this.dupFBSItems.findIndex((item) => item.itemID == event.data.itemID)
    if(arrayIndex >= 0) {
      this.dupFBSItems[arrayIndex].selected = false;
      //console.log(arrayIndex+"-"+this.dupFBSItems[arrayIndex].itemID+", Selected? "+this.dupFBSItems[arrayIndex].selected)
    }
    //console.log("Checked? "+this.selectedFbsItem.selected)
  }

  onRowExpand(event: TableRowExpandEvent) {
    const params = new HttpParams()
      .set('chqAcNo', event.data.chqAcNo)
      .set('serNo', event.data.serNo)
      .set('chqAmt', event.data.chqAmount)
      .set('chqDate', event.data.fbsDate)
    //console.log("Chq Data: "+params+" was expanded")
    this.fbsitemsService.getFBSDupData(params).subscribe(item => {
      this.dupFBSItems = item;
      //console.log(this.dupFBSItems)       //Display the array data....
    })
    //console.log("Row with id#: "+event.data.id+" was expanded")
  }

  //ButtonClick controls the expansion and clasping of the rows on the main table

  buttonClick() {
    //console.log("isExpanded: "+this.isExpanded)
    if (this.isExpanded) {
      //console.log("Clasping  :: isExpanded: "+this.isExpanded)
      this.expandedRows = {};
      //this.dupChqs.forEach(product => product && product.id ? this.expandedRows[product.id] = true : '');
    } else {
      //console.log("Expanding :: isExpanded: "+this.isExpanded)
      //this.dupChqs.forEach(product => product && product.id ? this.expandedRows[product.id] = true : '');
      //this.expandedRows = {};
    }
    this.isExpanded = !this.isExpanded;
    //console.log("isExpanded: "+this.isExpanded)
  }

  selectedRowItem(dupFBSItem: any) {
    //debugger
    this.selectedFbsItem = dupFBSItem;
    this.highlighted = dupFBSItem
    this.itemID = dupFBSItem.itemID
    //console.log(this.itemID)

    this.fbsitemsService.getFBSImageData(this.itemID).subscribe(item => {
      this.fbsImage = item
      this.fImage = this.fbsImage.frontImageData
      let fImgURL = 'data:image/png;base64,'+item.frontImageData;
      this.fImage = this.sanitizer.bypassSecurityTrustUrl(fImgURL);

      this.rImage = this.fbsImage.rearImageData
      let rImgURL = 'data:image/png;base64,'+item.rearImageData;
      this.rImage = this.sanitizer.bypassSecurityTrustUrl(rImgURL);
      //console.log(this.fImage)
    });
  }

  cbOnClick(dupFBSItem: any) {
    //console.log("cbOnClick..."+dupFBSItem.itemID)
  }

  markDupFBSItem() {
    //console.log("Mark Duplicate FBS Item..")
    //debugger
    let arraySize = this.dupFBSItems.length
    //console.log("Array size: "+arraySize)
    if (arraySize > 1) {
      for (let fbsItem of this.dupFBSItems) {
        //debugger
        //console.log(fbsItem.itemID+", selected: "+fbsItem.selected)
        if(fbsItem.selected) {
          this.httpRslt = this.fbsitemsService.putMarkDupItem(fbsItem.itemID).subscribe(resp => {
            console.log("Item "+fbsItem.itemID+" marked as duplicate..")
            this.messageService.add({ severity: 'info', summary: 'Item marked as duplicate', detail: fbsItem.itemID});
            //console.log("Response: "+resp)
          })
          this.buttonClick()
          //this.refreshDupList()
        }
      }
    } else
      this.messageService.add({ severity: 'info', summary: 'No records selected..'});
  }

  saveFBSItem() {
    //console.log("Save FBS Item...")
    //console.log(this.selectedFbsItem)
    this.fbsitemsService.updateFBSChqDet(this.selectedFbsItem).subscribe(data => {
      this.messageService.add({ severity: 'info', summary: 'Changes Saved', detail: this.selectedFbsItem.itemID});
      //console.log("Response: "+data)
    })
  }

}
