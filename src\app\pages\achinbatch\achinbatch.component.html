<h4 class = "font-bold flex justify-content-center" >INCOMING ACH BATCHES</h4>
<p-toast />
<p-confirmDialog />
<div class="grid">
  <div class="flex-auto">
    <div><label class="font-bold block ml-3 mb-1" style="font-size: 15px">ACH/Batch Date</label></div>
    <div class="inline-flex bottom-0">
      <p-calendar [(ngModel)]="batchDate"
                  [showIcon]="true"
                  [maxDate]="maxDate"
                  dateFormat="dd M,  yy"
                  inputId="calendarBtn"
                  class="p-inputtext-sm block ml-3 mb-2"
                  [style]="{'font-size': 'large'}"
                  (onSelect)="onDateSelected($event)">
      </p-calendar>
      <button class ="inline" pButton pRipple label="Generate Cruise ACH File" icon="pi pi-save" (click)="confirm($event)"
              style="margin-bottom: 15px; margin-left:30px"></button>

      <button class ="inline" pButton pRipple label="Print Batch List" icon="pi pi-print" (click)="printBatchReport()"
              style="margin-bottom: 15px; margin-left:30px"></button>

      <button class ="inline" pButton pRipple label="Print Checked Batches" icon="pi pi-print" (click)="printCheckedBatches()"
              style="margin-bottom: 15px; margin-left:30px"></button>

      <button class ="inline" pButton pRipple label="Refresh Batch List" icon="pi pi-refresh" (click)="refreshPage()"
              style="margin-bottom: 15px; margin-left:30px"></button>

      <button class ="inline" pButton pRipple label="Show PENDING Batches Only" icon="pi pi-refresh" (click)="pendingOnlyBatches()"
              style="margin-bottom: 15px; margin-left:30px"></button>
    </div>
  </div>
  <div class="col-12 card bg-black-alpha-20">
    <p-table [value]="batches"
             [(selection)]="selectedBatch"
             dataKey="recId"
             selectionMode="single"
             (onRowSelect)="onRowSelect($event)"
             (onRowUnselect)="onRowUnselect($event)"
             [tableStyle]="{'min-width': '50rem'}"
             [scrollable]="true"
             (onHeaderCheckboxToggle)="headerCheckboxToggle($event)"
             scrollHeight="490px"
             styleClass="p-datatable-sm p-datatable-gridlines">
      <ng-template  pTemplate="header">
        <tr>
          <th class= "bg-black-alpha-50 text-white" style="width: 2.5rem">
            <p-tableHeaderCheckbox/>
          </th>
          <th class= "bg-black-alpha-70 text-white" style="width: 4rem">ACH FILE</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 3rem">BATCH ID</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 6rem">CO. ID</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 8rem">CO. NAME</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 12rem">ORIG BANK</th>
          <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 9rem">CR TOTAL</th>
          <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 9rem">DR TOTAL</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 4rem">ITEMS</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 14rem">PROC DATE & TIME</th>
          <th class= "bg-black-alpha-70 text-white" style="width: 9rem">STATUS</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-batchItem>
        <tr [class.ui-state-highlight]="batchItem === highlighted">
        <!--tr [pSelectableRow]="batchItem"-->
          <td><input type="checkbox"
                     [(ngModel)]="batchItem.isSelected"
                     [value]="batchItem"
                     [disabled]="batchItem.cbEnabled"
                     (click)="cbOnClick(batchItem)"
                     (change)="OnChangeEvent(batchItem)"
                     style="width: 20px; height: 19px; margin-left: 0"/>
          </td>
          <td><a (click)="selectedRow(batchItem)"> {{batchItem.fileName}}</a></td>
          <td><a (click)="selectedRow(batchItem)"> {{batchItem.batchId}}</a></td>
          <td>{{batchItem.compID}}</td>
          <td>{{batchItem.compName}}</td>
          <td>{{batchItem.odfiName}}</td>
          <td style="text-align: right" inputmode="decimal">{{batchItem.crTotal | currency}}</td>
          <td style="text-align: right" inputmode="decimal">{{batchItem.drTotal | currency}}</td>
          <td>{{batchItem.entryCount}}</td>
          <td>{{batchItem.procTime | date: "dd-MMM-yyyy hh:mm:ss a"}}</td>
          <td>{{batchItem.recStatus}}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog header="Processing, pleas wait.."
          [(visible)]="showSpinnerDialog"
          [modal]="true"
          [style]="{ width: '20rem' }"
          position="center">
  <div class="card flex justify-content-center">
    <p-progressSpinner
      styleClass="w-4rem h-4rem"
      strokeWidth="8"
      fill="var(--surface-ground)"
      animationDuration=".5s" />
  </div>
</p-dialog>
