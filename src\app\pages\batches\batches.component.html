<h4 class = "font-bold flex justify-content-center" >BATCH LIST</h4>
<p-toast></p-toast>
<div class="grid">
  <div class="col-2">
    <button pButton label="Print Batch Details" icon="pi pi-chevron-left"
            style="margin-bottom: 15px; margin-left:10px; width: 200px"></button>
    <button pButton label="Print Batch List" icon="pi pi-chevron-left"
            style="margin-bottom: 15px; margin-left:10px; width: 200px"></button>
  </div>
  <div class="col-10">
    <div class="flex-auto">
      <label class="font-bold block ml-3 mb-1"> Batch Scan Date </label>
    </div>
    <div>
      <p-calendar [(ngModel)]="batchDate"
                  [showIcon]="true"
                  [maxDate]="maxDate"
                  dateFormat="dd M, yy"
                  inputId="calendarBtn"
                  class="p-inputtext-sm font-medium font-bold block justify-content-center ml-3 mb-2"
                  [style]="{'font-size': 'large'}"
                  (onSelect)="onDateSelected($event)">
      </p-calendar>
    </div>

    <div class="card class bg-black-alpha-20">
      <p-table [value]="batches"
               [(selection)]="selectedBatch"
               dataKey="batchId"
               selectionMode="single"
               (onRowSelect)="onRowSelect($event)"
               (onRowUnselect)="onRowUnselect($event)"
               [tableStyle]="{'min-width': '50rem'}"
               [scrollable]="true"
               scrollHeight="490px"
               styleClass="p-datatable-sm p-datatable-gridlines">
        <ng-template  pTemplate="header">
          <tr>
            <th class= "bg-black-alpha-50 text-white" style="width: 4rem"></th>
            <th class= "bg-black-alpha-70 text-white">BATCH ID</th>
            <th class= "bg-black-alpha-70 text-white">BRANCH CODE</th>
            <th class= "bg-black-alpha-70 text-white">TELLER ID</th>
            <th class= "bg-black-alpha-70 text-white" style="text-align: right; width: 20rem">BATCH TOTAL</th>
            <th class= "bg-black-alpha-70 text-white">ITEMS</th>
            <th class= "bg-black-alpha-70 text-white">SCAN TYPE</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-batchItem>
          <tr [class.ui-state-highlight]="batchItem === highlighted">
            <td>
              <p-tableCheckbox [value]="batchItem"
                               (click)="cbOnClick(batchItem)">
                </p-tableCheckbox>
            </td>
            <td>
              <a (click)="selectedRow(batchItem)"> {{batchItem.batchId}}</a>
            </td>
            <td>
              <a (click)="selectedRow(batchItem)"> {{batchItem.branchCode}}</a>
            </td>
            <td>{{batchItem.tellerID}}</td>
            <td style="text-align: right" inputmode="decimal">{{batchItem.depositAmount | currency}}</td>
            <td>{{batchItem.itemCount}}</td>
            <td>{{batchItem.scanType}}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>

